{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navbar as BootstrapNavbar, Nav, NavDropdown, Container, Button } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n  const handleAdminPanel = () => {\n    var _process$env$REACT_AP;\n    // Open admin panel in new tab to preserve current session\n    const adminUrl = ((_process$env$REACT_AP = process.env.REACT_APP_API_URL) === null || _process$env$REACT_AP === void 0 ? void 0 : _process$env$REACT_AP.replace('/api', '/admin')) || 'http://localhost:8000/admin';\n    window.open(adminUrl, '_blank');\n  };\n  return /*#__PURE__*/_jsxDEV(BootstrapNavbar, {\n    bg: \"dark\",\n    variant: \"dark\",\n    expand: \"lg\",\n    sticky: \"top\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(BootstrapNavbar.Brand, {\n        as: Link,\n        to: \"/\",\n        className: \"text-decoration-none\",\n        children: \"Full Stack CMS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BootstrapNavbar.Toggle, {\n        \"aria-controls\": \"basic-navbar-nav\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BootstrapNavbar.Collapse, {\n        id: \"basic-navbar-nav\",\n        children: [/*#__PURE__*/_jsxDEV(Nav, {\n          className: \"me-auto\",\n          children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/\",\n            className: \"text-decoration-none\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav, {\n          className: \"ms-auto\",\n          children: isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/dashboard\",\n              className: \"text-decoration-none\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(NavDropdown, {\n              title: (user === null || user === void 0 ? void 0 : user.name) || 'User',\n              id: \"user-dropdown\",\n              align: \"end\",\n              children: [/*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                onClick: () => navigate('/profile'),\n                children: \"Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                onClick: () => navigate('/profile/edit'),\n                children: \"Edit Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                onClick: handleLogout,\n                children: \"Logout\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 17\n            }, this), !(user !== null && user !== void 0 && user.email_verified_at) && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-warning\",\n              size: \"sm\",\n              className: \"ms-2\",\n              onClick: () => navigate('/email-verification'),\n              children: \"Verify Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/login\",\n              className: \"text-decoration-none\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              className: \"ms-2\",\n              onClick: () => navigate('/register'),\n              children: \"Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"B5aHiu91piX0w1CsOFDPXF/GbhU=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "BootstrapNavbar", "Nav", "NavDropdown", "Container", "<PERSON><PERSON>", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "_s", "user", "isAuthenticated", "logout", "navigate", "handleLogout", "error", "console", "handleAdminPanel", "_process$env$REACT_AP", "adminUrl", "process", "env", "REACT_APP_API_URL", "replace", "window", "open", "bg", "variant", "expand", "sticky", "children", "Brand", "as", "to", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Toggle", "Collapse", "id", "title", "name", "align", "<PERSON><PERSON>", "onClick", "Divider", "email_verified_at", "size", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/layout/Navbar.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navbar as BootstrapN<PERSON>bar, Nav, NavDropdown, Container, But<PERSON> } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst Navbar: React.FC = () => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const navigate = useNavigate();\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const handleAdminPanel = () => {\n    // Open admin panel in new tab to preserve current session\n    const adminUrl = process.env.REACT_APP_API_URL?.replace('/api', '/admin') || 'http://localhost:8000/admin';\n    window.open(adminUrl, '_blank');\n  };\n\n  return (\n    <BootstrapNavbar bg=\"dark\" variant=\"dark\" expand=\"lg\" sticky=\"top\">\n      <Container>\n        <BootstrapNavbar.Brand as={Link} to=\"/\" className=\"text-decoration-none\">\n          Full Stack CMS\n        </BootstrapNavbar.Brand>\n\n        <BootstrapNavbar.Toggle aria-controls=\"basic-navbar-nav\" />\n        <BootstrapNavbar.Collapse id=\"basic-navbar-nav\">\n          <Nav className=\"me-auto\">\n            <Nav.Link as={Link} to=\"/\" className=\"text-decoration-none\">\n              Home\n            </Nav.Link>\n          </Nav>\n\n          <Nav className=\"ms-auto\">\n            {isAuthenticated ? (\n              <>\n                <Nav.Link as={Link} to=\"/dashboard\" className=\"text-decoration-none\">\n                  Dashboard\n                </Nav.Link>\n                <NavDropdown\n                  title={user?.name || 'User'}\n                  id=\"user-dropdown\"\n                  align=\"end\"\n                >\n                  <NavDropdown.Item onClick={() => navigate('/profile')}>\n                    Profile\n                  </NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => navigate('/profile/edit')}>\n                    Edit Profile\n                  </NavDropdown.Item>\n                  <NavDropdown.Divider />\n                  <NavDropdown.Item onClick={handleLogout}>\n                    Logout\n                  </NavDropdown.Item>\n                </NavDropdown>\n\n                {!user?.email_verified_at && (\n                  <Button\n                    variant=\"outline-warning\"\n                    size=\"sm\"\n                    className=\"ms-2\"\n                    onClick={() => navigate('/email-verification')}\n                  >\n                    Verify Email\n                  </Button>\n                )}\n              </>\n            ) : (\n              <>\n                <Nav.Link as={Link} to=\"/login\" className=\"text-decoration-none\">\n                  Login\n                </Nav.Link>\n                <Button\n                  variant=\"primary\"\n                  className=\"ms-2\"\n                  onClick={() => navigate('/register')}\n                >\n                  Register\n                </Button>\n              </>\n            )}\n          </Nav>\n        </BootstrapNavbar.Collapse>\n      </Container>\n    </BootstrapNavbar>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,IAAIC,eAAe,EAAEC,GAAG,EAAEC,WAAW,EAAEC,SAAS,EAAEC,MAAM,QAAQ,iBAAiB;AAChG,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMZ,MAAgB,GAAGA,CAAA,KAAM;EAAAa,EAAA;EAC7B,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGR,OAAO,CAAC,CAAC;EACnD,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMF,MAAM,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC;EACF,CAAC;EAED,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC7B;IACA,MAAMC,QAAQ,GAAG,EAAAD,qBAAA,GAAAE,OAAO,CAACC,GAAG,CAACC,iBAAiB,cAAAJ,qBAAA,uBAA7BA,qBAAA,CAA+BK,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAI,6BAA6B;IAC1GC,MAAM,CAACC,IAAI,CAACN,QAAQ,EAAE,QAAQ,CAAC;EACjC,CAAC;EAED,oBACEb,OAAA,CAACT,eAAe;IAAC6B,EAAE,EAAC,MAAM;IAACC,OAAO,EAAC,MAAM;IAACC,MAAM,EAAC,IAAI;IAACC,MAAM,EAAC,KAAK;IAAAC,QAAA,eAChExB,OAAA,CAACN,SAAS;MAAA8B,QAAA,gBACRxB,OAAA,CAACT,eAAe,CAACkC,KAAK;QAACC,EAAE,EAAE9B,IAAK;QAAC+B,EAAE,EAAC,GAAG;QAACC,SAAS,EAAC,sBAAsB;QAAAJ,QAAA,EAAC;MAEzE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAuB,CAAC,eAExBhC,OAAA,CAACT,eAAe,CAAC0C,MAAM;QAAC,iBAAc;MAAkB;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DhC,OAAA,CAACT,eAAe,CAAC2C,QAAQ;QAACC,EAAE,EAAC,kBAAkB;QAAAX,QAAA,gBAC7CxB,OAAA,CAACR,GAAG;UAACoC,SAAS,EAAC,SAAS;UAAAJ,QAAA,eACtBxB,OAAA,CAACR,GAAG,CAACI,IAAI;YAAC8B,EAAE,EAAE9B,IAAK;YAAC+B,EAAE,EAAC,GAAG;YAACC,SAAS,EAAC,sBAAsB;YAAAJ,QAAA,EAAC;UAE5D;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENhC,OAAA,CAACR,GAAG;UAACoC,SAAS,EAAC,SAAS;UAAAJ,QAAA,EACrBnB,eAAe,gBACdL,OAAA,CAAAE,SAAA;YAAAsB,QAAA,gBACExB,OAAA,CAACR,GAAG,CAACI,IAAI;cAAC8B,EAAE,EAAE9B,IAAK;cAAC+B,EAAE,EAAC,YAAY;cAACC,SAAS,EAAC,sBAAsB;cAAAJ,QAAA,EAAC;YAErE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXhC,OAAA,CAACP,WAAW;cACV2C,KAAK,EAAE,CAAAhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,IAAI,KAAI,MAAO;cAC5BF,EAAE,EAAC,eAAe;cAClBG,KAAK,EAAC,KAAK;cAAAd,QAAA,gBAEXxB,OAAA,CAACP,WAAW,CAAC8C,IAAI;gBAACC,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,UAAU,CAAE;gBAAAiB,QAAA,EAAC;cAEvD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC,eACnBhC,OAAA,CAACP,WAAW,CAAC8C,IAAI;gBAACC,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,eAAe,CAAE;gBAAAiB,QAAA,EAAC;cAE5D;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC,eACnBhC,OAAA,CAACP,WAAW,CAACgD,OAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvBhC,OAAA,CAACP,WAAW,CAAC8C,IAAI;gBAACC,OAAO,EAAEhC,YAAa;gBAAAgB,QAAA,EAAC;cAEzC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAEb,EAAC5B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEsC,iBAAiB,kBACvB1C,OAAA,CAACL,MAAM;cACL0B,OAAO,EAAC,iBAAiB;cACzBsB,IAAI,EAAC,IAAI;cACTf,SAAS,EAAC,MAAM;cAChBY,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,qBAAqB,CAAE;cAAAiB,QAAA,EAChD;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA,eACD,CAAC,gBAEHhC,OAAA,CAAAE,SAAA;YAAAsB,QAAA,gBACExB,OAAA,CAACR,GAAG,CAACI,IAAI;cAAC8B,EAAE,EAAE9B,IAAK;cAAC+B,EAAE,EAAC,QAAQ;cAACC,SAAS,EAAC,sBAAsB;cAAAJ,QAAA,EAAC;YAEjE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXhC,OAAA,CAACL,MAAM;cACL0B,OAAO,EAAC,SAAS;cACjBO,SAAS,EAAC,MAAM;cAChBY,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,WAAW,CAAE;cAAAiB,QAAA,EACtC;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACkB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAAC7B,EAAA,CAtFIb,MAAgB;EAAA,QACsBQ,OAAO,EAChCD,WAAW;AAAA;AAAA+C,EAAA,GAFxBtD,MAAgB;AAwFtB,eAAeA,MAAM;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}