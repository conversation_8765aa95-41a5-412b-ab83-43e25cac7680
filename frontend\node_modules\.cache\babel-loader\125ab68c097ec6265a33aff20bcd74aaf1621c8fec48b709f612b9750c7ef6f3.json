{"ast": null, "code": "import React from'react';import{Navbar as Bootstra<PERSON><PERSON><PERSON><PERSON>,Nav,NavDropdown,Container,But<PERSON>}from'react-bootstrap';import{Link,useNavigate}from'react-router-dom';import{useAuth}from'../../contexts/AuthContext';import authService from'../../services/authService';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const Navbar=()=>{const{user,isAuthenticated,logout}=useAuth();const navigate=useNavigate();const handleLogout=async()=>{try{await logout();}catch(error){console.error('Logout error:',error);}};const handleAdminPanel=async()=>{try{await authService.navigateToAdminPanel();}catch(error){var _process$env$REACT_AP;console.error('Failed to access admin panel:',error);// Fallback to direct navigation (user will need to login manually)\nconst baseUrl=((_process$env$REACT_AP=process.env.REACT_APP_API_URL)===null||_process$env$REACT_AP===void 0?void 0:_process$env$REACT_AP.replace('/api',''))||'http://localhost:8000';const adminUrl=`${baseUrl}/admin`;window.open(adminUrl,'_blank');}};return/*#__PURE__*/_jsx(BootstrapNavbar,{bg:\"dark\",variant:\"dark\",expand:\"lg\",sticky:\"top\",children:/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(BootstrapNavbar.Brand,{as:Link,to:\"/\",className:\"text-decoration-none\",children:\"Full Stack CMS\"}),/*#__PURE__*/_jsx(BootstrapNavbar.Toggle,{\"aria-controls\":\"basic-navbar-nav\"}),/*#__PURE__*/_jsxs(BootstrapNavbar.Collapse,{id:\"basic-navbar-nav\",children:[/*#__PURE__*/_jsx(Nav,{className:\"me-auto\",children:/*#__PURE__*/_jsx(Nav.Link,{as:Link,to:\"/\",className:\"text-decoration-none\",children:\"Home\"})}),/*#__PURE__*/_jsx(Nav,{className:\"ms-auto\",children:isAuthenticated?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Nav.Link,{as:Link,to:\"/dashboard\",className:\"text-decoration-none\",children:\"Dashboard\"}),/*#__PURE__*/_jsxs(NavDropdown,{title:(user===null||user===void 0?void 0:user.name)||'User',id:\"user-dropdown\",align:\"end\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>navigate('/profile'),children:\"Profile\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>navigate('/profile/edit'),children:\"Edit Profile\"}),(user===null||user===void 0?void 0:user.role)==='admin'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(NavDropdown.Divider,{}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:handleAdminPanel,children:\"Admin Panel\"})]}),/*#__PURE__*/_jsx(NavDropdown.Divider,{}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:handleLogout,children:\"Logout\"})]}),!(user!==null&&user!==void 0&&user.email_verified_at)&&/*#__PURE__*/_jsx(Button,{variant:\"outline-warning\",size:\"sm\",className:\"ms-2\",onClick:()=>navigate('/email-verification'),children:\"Verify Email\"})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Nav.Link,{as:Link,to:\"/login\",className:\"text-decoration-none\",children:\"Login\"}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",className:\"ms-2\",onClick:()=>navigate('/register'),children:\"Register\"})]})})]})]})});};export default Navbar;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "BootstrapNavbar", "Nav", "NavDropdown", "Container", "<PERSON><PERSON>", "Link", "useNavigate", "useAuth", "authService", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "user", "isAuthenticated", "logout", "navigate", "handleLogout", "error", "console", "handleAdminPanel", "navigateToAdminPanel", "_process$env$REACT_AP", "baseUrl", "process", "env", "REACT_APP_API_URL", "replace", "adminUrl", "window", "open", "bg", "variant", "expand", "sticky", "children", "Brand", "as", "to", "className", "Toggle", "Collapse", "id", "title", "name", "align", "<PERSON><PERSON>", "onClick", "role", "Divider", "email_verified_at", "size"], "sources": ["C:/laragon/www/frontend/src/components/layout/Navbar.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navbar as BootstrapNavbar, Nav, NavDropdown, Container, Button } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport authService from '../../services/authService';\n\nconst Navbar: React.FC = () => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const navigate = useNavigate();\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const handleAdminPanel = async () => {\n    try {\n      await authService.navigateToAdminPanel();\n    } catch (error: any) {\n      console.error('Failed to access admin panel:', error);\n      // Fallback to direct navigation (user will need to login manually)\n      const baseUrl = process.env.REACT_APP_API_URL?.replace('/api', '') || 'http://localhost:8000';\n      const adminUrl = `${baseUrl}/admin`;\n      window.open(adminUrl, '_blank');\n    }\n  };\n\n  return (\n    <BootstrapNavbar bg=\"dark\" variant=\"dark\" expand=\"lg\" sticky=\"top\">\n      <Container>\n        <BootstrapNavbar.Brand as={Link} to=\"/\" className=\"text-decoration-none\">\n          Full Stack CMS\n        </BootstrapNavbar.Brand>\n\n        <BootstrapNavbar.Toggle aria-controls=\"basic-navbar-nav\" />\n        <BootstrapNavbar.Collapse id=\"basic-navbar-nav\">\n          <Nav className=\"me-auto\">\n            <Nav.Link as={Link} to=\"/\" className=\"text-decoration-none\">\n              Home\n            </Nav.Link>\n          </Nav>\n\n          <Nav className=\"ms-auto\">\n            {isAuthenticated ? (\n              <>\n                <Nav.Link as={Link} to=\"/dashboard\" className=\"text-decoration-none\">\n                  Dashboard\n                </Nav.Link>\n                <NavDropdown\n                  title={user?.name || 'User'}\n                  id=\"user-dropdown\"\n                  align=\"end\"\n                >\n                  <NavDropdown.Item onClick={() => navigate('/profile')}>\n                    Profile\n                  </NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => navigate('/profile/edit')}>\n                    Edit Profile\n                  </NavDropdown.Item>\n                  {user?.role === 'admin' && (\n                    <>\n                      <NavDropdown.Divider />\n                      <NavDropdown.Item onClick={handleAdminPanel}>\n                        Admin Panel\n                      </NavDropdown.Item>\n                    </>\n                  )}\n                  <NavDropdown.Divider />\n                  <NavDropdown.Item onClick={handleLogout}>\n                    Logout\n                  </NavDropdown.Item>\n                </NavDropdown>\n\n                {!user?.email_verified_at && (\n                  <Button\n                    variant=\"outline-warning\"\n                    size=\"sm\"\n                    className=\"ms-2\"\n                    onClick={() => navigate('/email-verification')}\n                  >\n                    Verify Email\n                  </Button>\n                )}\n              </>\n            ) : (\n              <>\n                <Nav.Link as={Link} to=\"/login\" className=\"text-decoration-none\">\n                  Login\n                </Nav.Link>\n                <Button\n                  variant=\"primary\"\n                  className=\"ms-2\"\n                  onClick={() => navigate('/register')}\n                >\n                  Register\n                </Button>\n              </>\n            )}\n          </Nav>\n        </BootstrapNavbar.Collapse>\n      </Container>\n    </BootstrapNavbar>\n  );\n};\n\nexport default Navbar;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,GAAI,CAAAC,eAAe,CAAEC,GAAG,CAAEC,WAAW,CAAEC,SAAS,CAAEC,MAAM,KAAQ,iBAAiB,CAChG,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,OAAO,KAAQ,4BAA4B,CACpD,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErD,KAAM,CAAAf,MAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAEgB,IAAI,CAAEC,eAAe,CAAEC,MAAO,CAAC,CAAGV,OAAO,CAAC,CAAC,CACnD,KAAM,CAAAW,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAa,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAF,MAAM,CAAC,CAAC,CAChB,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACvC,CACF,CAAC,CAED,KAAM,CAAAE,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAAd,WAAW,CAACe,oBAAoB,CAAC,CAAC,CAC1C,CAAE,MAAOH,KAAU,CAAE,KAAAI,qBAAA,CACnBH,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD;AACA,KAAM,CAAAK,OAAO,CAAG,EAAAD,qBAAA,CAAAE,OAAO,CAACC,GAAG,CAACC,iBAAiB,UAAAJ,qBAAA,iBAA7BA,qBAAA,CAA+BK,OAAO,CAAC,MAAM,CAAE,EAAE,CAAC,GAAI,uBAAuB,CAC7F,KAAM,CAAAC,QAAQ,CAAG,GAAGL,OAAO,QAAQ,CACnCM,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAE,QAAQ,CAAC,CACjC,CACF,CAAC,CAED,mBACEpB,IAAA,CAACV,eAAe,EAACiC,EAAE,CAAC,MAAM,CAACC,OAAO,CAAC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACC,MAAM,CAAC,KAAK,CAAAC,QAAA,cAChEvB,KAAA,CAACX,SAAS,EAAAkC,QAAA,eACR3B,IAAA,CAACV,eAAe,CAACsC,KAAK,EAACC,EAAE,CAAElC,IAAK,CAACmC,EAAE,CAAC,GAAG,CAACC,SAAS,CAAC,sBAAsB,CAAAJ,QAAA,CAAC,gBAEzE,CAAuB,CAAC,cAExB3B,IAAA,CAACV,eAAe,CAAC0C,MAAM,EAAC,gBAAc,kBAAkB,CAAE,CAAC,cAC3D5B,KAAA,CAACd,eAAe,CAAC2C,QAAQ,EAACC,EAAE,CAAC,kBAAkB,CAAAP,QAAA,eAC7C3B,IAAA,CAACT,GAAG,EAACwC,SAAS,CAAC,SAAS,CAAAJ,QAAA,cACtB3B,IAAA,CAACT,GAAG,CAACI,IAAI,EAACkC,EAAE,CAAElC,IAAK,CAACmC,EAAE,CAAC,GAAG,CAACC,SAAS,CAAC,sBAAsB,CAAAJ,QAAA,CAAC,MAE5D,CAAU,CAAC,CACR,CAAC,cAEN3B,IAAA,CAACT,GAAG,EAACwC,SAAS,CAAC,SAAS,CAAAJ,QAAA,CACrBrB,eAAe,cACdF,KAAA,CAAAF,SAAA,EAAAyB,QAAA,eACE3B,IAAA,CAACT,GAAG,CAACI,IAAI,EAACkC,EAAE,CAAElC,IAAK,CAACmC,EAAE,CAAC,YAAY,CAACC,SAAS,CAAC,sBAAsB,CAAAJ,QAAA,CAAC,WAErE,CAAU,CAAC,cACXvB,KAAA,CAACZ,WAAW,EACV2C,KAAK,CAAE,CAAA9B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE+B,IAAI,GAAI,MAAO,CAC5BF,EAAE,CAAC,eAAe,CAClBG,KAAK,CAAC,KAAK,CAAAV,QAAA,eAEX3B,IAAA,CAACR,WAAW,CAAC8C,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAM/B,QAAQ,CAAC,UAAU,CAAE,CAAAmB,QAAA,CAAC,SAEvD,CAAkB,CAAC,cACnB3B,IAAA,CAACR,WAAW,CAAC8C,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAM/B,QAAQ,CAAC,eAAe,CAAE,CAAAmB,QAAA,CAAC,cAE5D,CAAkB,CAAC,CAClB,CAAAtB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEmC,IAAI,IAAK,OAAO,eACrBpC,KAAA,CAAAF,SAAA,EAAAyB,QAAA,eACE3B,IAAA,CAACR,WAAW,CAACiD,OAAO,GAAE,CAAC,cACvBzC,IAAA,CAACR,WAAW,CAAC8C,IAAI,EAACC,OAAO,CAAE3B,gBAAiB,CAAAe,QAAA,CAAC,aAE7C,CAAkB,CAAC,EACnB,CACH,cACD3B,IAAA,CAACR,WAAW,CAACiD,OAAO,GAAE,CAAC,cACvBzC,IAAA,CAACR,WAAW,CAAC8C,IAAI,EAACC,OAAO,CAAE9B,YAAa,CAAAkB,QAAA,CAAC,QAEzC,CAAkB,CAAC,EACR,CAAC,CAEb,EAACtB,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEqC,iBAAiB,gBACvB1C,IAAA,CAACN,MAAM,EACL8B,OAAO,CAAC,iBAAiB,CACzBmB,IAAI,CAAC,IAAI,CACTZ,SAAS,CAAC,MAAM,CAChBQ,OAAO,CAAEA,CAAA,GAAM/B,QAAQ,CAAC,qBAAqB,CAAE,CAAAmB,QAAA,CAChD,cAED,CAAQ,CACT,EACD,CAAC,cAEHvB,KAAA,CAAAF,SAAA,EAAAyB,QAAA,eACE3B,IAAA,CAACT,GAAG,CAACI,IAAI,EAACkC,EAAE,CAAElC,IAAK,CAACmC,EAAE,CAAC,QAAQ,CAACC,SAAS,CAAC,sBAAsB,CAAAJ,QAAA,CAAC,OAEjE,CAAU,CAAC,cACX3B,IAAA,CAACN,MAAM,EACL8B,OAAO,CAAC,SAAS,CACjBO,SAAS,CAAC,MAAM,CAChBQ,OAAO,CAAEA,CAAA,GAAM/B,QAAQ,CAAC,WAAW,CAAE,CAAAmB,QAAA,CACtC,UAED,CAAQ,CAAC,EACT,CACH,CACE,CAAC,EACkB,CAAC,EAClB,CAAC,CACG,CAAC,CAEtB,CAAC,CAED,cAAe,CAAAtC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}