import api, { endpoints } from './api';

export interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  bio?: string;
  avatar?: string;
  date_of_birth?: string;
  role: 'admin' | 'user';
  is_active: boolean;
  email_verified_at?: string;
  created_at: string;
  updated_at: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  phone?: string;
  date_of_birth?: string;
}

export interface AuthResponse {
  message: string;
  user: User;
  token: string;
}

export interface UpdateProfileData {
  name?: string;
  email?: string;
  phone?: string;
  bio?: string;
  date_of_birth?: string;
  current_password?: string;
  password?: string;
  password_confirmation?: string;
}

class AuthService {
  // Register new user
  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await api.post(endpoints.register, data);
    const { user, token } = response.data;

    if (token) {
      this.setAuthData(user, token);
    }

    return response.data;
  }

  // Login user
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await api.post(endpoints.login, credentials);
    const { user, token } = response.data;

    if (token) {
      this.setAuthData(user, token);
    }

    return response.data;
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      await api.post(endpoints.logout);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearAuthData();
    }
  }

  // Get current user profile
  async getProfile(): Promise<User> {
    const response = await api.get(endpoints.profile);
    return response.data.user;
  }

  // Update user profile
  async updateProfile(data: UpdateProfileData): Promise<User> {
    const response = await api.put(endpoints.updateProfile, data);
    const user = response.data.user;

    // Update stored user data
    localStorage.setItem('user', JSON.stringify(user));

    return user;
  }

  // Upload avatar
  async uploadAvatar(file: File): Promise<User> {
    const formData = new FormData();
    formData.append('avatar', file);

    const response = await api.post(endpoints.uploadAvatar, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    const user = response.data.user;
    localStorage.setItem('user', JSON.stringify(user));

    return user;
  }

  // Forgot password
  async forgotPassword(email: string): Promise<{ message: string }> {
    const response = await api.post(endpoints.forgotPassword, { email });
    return response.data;
  }

  // Reset password
  async resetPassword(data: {
    token: string;
    email: string;
    password: string;
    password_confirmation: string;
  }): Promise<{ message: string }> {
    const response = await api.post(endpoints.resetPassword, data);
    return response.data;
  }

  // Resend email verification
  async resendVerification(): Promise<{ message: string }> {
    const response = await api.post(endpoints.resendVerification);
    return response.data;
  }

  // Create admin session and navigate to admin panel
  async navigateToAdminPanel(): Promise<void> {
    try {
      const response = await api.post(endpoints.adminSession);
      if (response.data.success) {
        // Open SSO URL in new tab - this will create the web session and redirect to admin
        const ssoUrl = response.data.admin_url;
        window.open(ssoUrl, '_blank');
      } else {
        throw new Error(response.data.message || 'Failed to create admin session');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to access admin panel');
    }
  }

  // Helper methods
  setAuthData(user: User, token: string): void {
    localStorage.setItem('auth_token', token);
    localStorage.setItem('user', JSON.stringify(user));
  }

  clearAuthData(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
  }

  getStoredUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  getStoredToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  isAuthenticated(): boolean {
    return !!this.getStoredToken();
  }

  isEmailVerified(): boolean {
    const user = this.getStoredUser();
    return !!user?.email_verified_at;
  }
}

const authService = new AuthService();
export default authService;
