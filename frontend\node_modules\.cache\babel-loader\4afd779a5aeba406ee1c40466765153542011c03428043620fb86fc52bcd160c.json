{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DattaAbleHeader.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Navbar, Nav, Dropdown, Button } from 'react-bootstrap';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport authService from '../../services/authService';\nimport dattaAbleTheme from '../../theme/dattaAbleTheme';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DattaAbleHeader = ({\n  onToggleSidebar,\n  onToggleSidebarCollapse,\n  sidebarCollapsed\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [showProfileDropdown, setShowProfileDropdown] = useState(false);\n  const handleLogout = async () => {\n    await logout();\n    navigate('/login');\n  };\n  const handleAdminPanel = async () => {\n    try {\n      await authService.navigateToAdminPanel();\n    } catch (error) {\n      var _process$env$REACT_AP;\n      console.error('Failed to access admin panel:', error);\n      // Fallback to direct navigation\n      const adminUrl = ((_process$env$REACT_AP = process.env.REACT_APP_API_URL) === null || _process$env$REACT_AP === void 0 ? void 0 : _process$env$REACT_AP.replace('/api', '/admin')) || 'http://localhost:8000/admin';\n      window.open(adminUrl, '_blank');\n    }\n  };\n  const headerStyles = {\n    position: 'fixed',\n    top: 0,\n    right: 0,\n    left: sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width,\n    height: dattaAbleTheme.layout.header.height,\n    backgroundColor: dattaAbleTheme.colors.background.paper,\n    borderBottom: `1px solid ${dattaAbleTheme.colors.border}`,\n    boxShadow: dattaAbleTheme.shadows.sm,\n    zIndex: 1030,\n    transition: 'left 0.3s ease',\n    padding: 0\n  };\n  const mobileHeaderStyles = {\n    ...headerStyles,\n    left: 0\n  };\n  const navbarStyles = {\n    height: '100%',\n    padding: `0 ${dattaAbleTheme.spacing[4]}`\n  };\n  const toggleButtonStyles = {\n    backgroundColor: 'transparent',\n    border: 'none',\n    color: dattaAbleTheme.colors.text.primary,\n    fontSize: '1.25rem',\n    padding: dattaAbleTheme.spacing[2],\n    borderRadius: dattaAbleTheme.borderRadius.md,\n    transition: 'all 0.2s ease'\n  };\n  const userAvatarStyles = {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    backgroundColor: dattaAbleTheme.colors.primary.main,\n    color: 'white',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontSize: '0.875rem',\n    fontWeight: dattaAbleTheme.typography.fontWeight.medium\n  };\n  const getUserInitials = name => {\n    if (!name) return 'U';\n    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: window.innerWidth < 768 ? mobileHeaderStyles : headerStyles,\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {\n      expand: \"lg\",\n      style: navbarStyles,\n      className: \"px-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          style: toggleButtonStyles,\n          onClick: onToggleSidebar,\n          className: \"d-lg-none me-2\",\n          onMouseEnter: e => {\n            e.target.style.backgroundColor = dattaAbleTheme.colors.background.light;\n          },\n          onMouseLeave: e => {\n            e.target.style.backgroundColor = 'transparent';\n          },\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-bars\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          style: toggleButtonStyles,\n          onClick: onToggleSidebarCollapse,\n          className: \"d-none d-lg-block me-3\",\n          onMouseEnter: e => {\n            e.target.style.backgroundColor = dattaAbleTheme.colors.background.light;\n          },\n          onMouseLeave: e => {\n            e.target.style.backgroundColor = 'transparent';\n          },\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: `fas ${sidebarCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mb-0 text-dark fw-semibold\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {\n        className: \"ms-auto d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n          className: \"me-3\",\n          children: [/*#__PURE__*/_jsxDEV(Dropdown.Toggle, {\n            as: \"button\",\n            style: {\n              ...toggleButtonStyles,\n              position: 'relative'\n            },\n            className: \"position-relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-bell\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                top: '4px',\n                right: '4px',\n                width: '8px',\n                height: '8px',\n                backgroundColor: dattaAbleTheme.colors.error.main,\n                borderRadius: '50%'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown.Menu, {\n            align: \"end\",\n            style: {\n              minWidth: '300px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Dropdown.Header, {\n              children: \"Notifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '32px',\n                      height: '32px',\n                      backgroundColor: dattaAbleTheme.colors.success.main,\n                      borderRadius: '50%',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-check text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-grow-1 ms-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: \"Welcome to Dashboard\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0 text-muted small\",\n                    children: \"Your dashboard is ready to use\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                children: \"View all notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          show: showProfileDropdown,\n          onToggle: setShowProfileDropdown,\n          children: [/*#__PURE__*/_jsxDEV(Dropdown.Toggle, {\n            as: \"div\",\n            style: {\n              cursor: 'pointer'\n            },\n            className: \"d-flex align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: userAvatarStyles,\n              children: getUserInitials(user === null || user === void 0 ? void 0 : user.name)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ms-2 d-none d-sm-block\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: dattaAbleTheme.typography.fontWeight.medium,\n                  color: dattaAbleTheme.colors.text.primary\n                },\n                children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: dattaAbleTheme.colors.text.secondary\n                },\n                children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-chevron-down ms-2 text-muted\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown.Menu, {\n            align: \"end\",\n            style: {\n              minWidth: '200px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Dropdown.Header, {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: userAvatarStyles,\n                  className: \"mx-auto mb-2\",\n                  children: getUserInitials(user === null || user === void 0 ? void 0 : user.name)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"mb-0\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n              onClick: () => navigate('/profile'),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-user me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), \"Profile\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n              onClick: () => navigate('/profile/edit'),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-cog me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), \"Settings\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), (user === null || user === void 0 ? void 0 : user.role) === 'admin' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Dropdown.Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n                onClick: handleAdminPanel,\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-shield-alt me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this), \"Admin Panel\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Dropdown.Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n              onClick: handleLogout,\n              className: \"text-danger\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-sign-out-alt me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), \"Logout\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"link\", {\n      rel: \"stylesheet\",\n      href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        .dropdown-toggle::after {\n          display: none;\n        }\n\n        .dropdown-menu {\n          border: 1px solid ${dattaAbleTheme.colors.border};\n          border-radius: ${dattaAbleTheme.borderRadius.lg};\n          box-shadow: ${dattaAbleTheme.shadows.lg};\n          padding: ${dattaAbleTheme.spacing[2]};\n        }\n\n        .dropdown-item {\n          border-radius: ${dattaAbleTheme.borderRadius.md};\n          padding: ${dattaAbleTheme.spacing[2]} ${dattaAbleTheme.spacing[3]};\n          transition: all 0.2s ease;\n        }\n\n        .dropdown-item:hover {\n          background-color: ${dattaAbleTheme.colors.background.light};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .dropdown-header {\n          padding: ${dattaAbleTheme.spacing[3]};\n          font-weight: ${dattaAbleTheme.typography.fontWeight.semibold};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        @media (max-width: 767.98px) {\n          .header-mobile {\n            left: 0 !important;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(DattaAbleHeader, \"NJMYJbz6pN5Sg7yKzN584bp+t8s=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = DattaAbleHeader;\nexport default DattaAbleHeader;\nvar _c;\n$RefreshReg$(_c, \"DattaAbleHeader\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON>", "Nav", "Dropdown", "<PERSON><PERSON>", "useNavigate", "useAuth", "authService", "dattaAbleTheme", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Datta<PERSON>bleHeader", "onToggleSidebar", "onToggleSidebarCollapse", "sidebarCollapsed", "_s", "navigate", "user", "logout", "showProfileDropdown", "setShowProfileDropdown", "handleLogout", "handleAdminPanel", "navigateToAdminPanel", "error", "_process$env$REACT_AP", "console", "adminUrl", "process", "env", "REACT_APP_API_URL", "replace", "window", "open", "headerStyles", "position", "top", "right", "left", "layout", "sidebar", "collapsedWidth", "width", "height", "header", "backgroundColor", "colors", "background", "paper", "borderBottom", "border", "boxShadow", "shadows", "sm", "zIndex", "transition", "padding", "mobileHeaderStyles", "navbarStyles", "spacing", "toggleButtonStyles", "color", "text", "primary", "fontSize", "borderRadius", "md", "userAvatarStyles", "main", "display", "alignItems", "justifyContent", "fontWeight", "typography", "medium", "getUserInitials", "name", "split", "map", "n", "join", "toUpperCase", "slice", "style", "innerWidth", "children", "expand", "className", "onClick", "onMouseEnter", "e", "target", "light", "onMouseLeave", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Toggle", "as", "<PERSON><PERSON>", "align", "min<PERSON><PERSON><PERSON>", "Header", "<PERSON><PERSON>", "success", "Divider", "show", "onToggle", "cursor", "secondary", "email", "role", "rel", "href", "lg", "semibold", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/DattaAbleHeader.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Navbar, Nav, Dropdown, Button } from 'react-bootstrap';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport authService from '../../services/authService';\nimport dattaAbleTheme from '../../theme/dattaAbleTheme';\n\ninterface DattaAbleHeaderProps {\n  onToggleSidebar: () => void;\n  onToggleSidebarCollapse: () => void;\n  sidebarCollapsed: boolean;\n}\n\nconst DattaAbleHeader: React.FC<DattaAbleHeaderProps> = ({ onToggleSidebar, onToggleSidebarCollapse, sidebarCollapsed }) => {\n  const navigate = useNavigate();\n  const { user, logout } = useAuth();\n  const [showProfileDropdown, setShowProfileDropdown] = useState(false);\n\n  const handleLogout = async () => {\n    await logout();\n    navigate('/login');\n  };\n\n  const handleAdminPanel = async () => {\n    try {\n      await authService.navigateToAdminPanel();\n    } catch (error: any) {\n      console.error('Failed to access admin panel:', error);\n      // Fallback to direct navigation\n      const adminUrl = process.env.REACT_APP_API_URL?.replace('/api', '/admin') || 'http://localhost:8000/admin';\n      window.open(adminUrl, '_blank');\n    }\n  };\n\n  const headerStyles: React.CSSProperties = {\n    position: 'fixed',\n    top: 0,\n    right: 0,\n    left: sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width,\n    height: dattaAbleTheme.layout.header.height,\n    backgroundColor: dattaAbleTheme.colors.background.paper,\n    borderBottom: `1px solid ${dattaAbleTheme.colors.border}`,\n    boxShadow: dattaAbleTheme.shadows.sm,\n    zIndex: 1030,\n    transition: 'left 0.3s ease',\n    padding: 0,\n  };\n\n  const mobileHeaderStyles: React.CSSProperties = {\n    ...headerStyles,\n    left: 0,\n  };\n\n  const navbarStyles: React.CSSProperties = {\n    height: '100%',\n    padding: `0 ${dattaAbleTheme.spacing[4]}`,\n  };\n\n  const toggleButtonStyles: React.CSSProperties = {\n    backgroundColor: 'transparent',\n    border: 'none',\n    color: dattaAbleTheme.colors.text.primary,\n    fontSize: '1.25rem',\n    padding: dattaAbleTheme.spacing[2],\n    borderRadius: dattaAbleTheme.borderRadius.md,\n    transition: 'all 0.2s ease',\n  };\n\n  const userAvatarStyles: React.CSSProperties = {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    backgroundColor: dattaAbleTheme.colors.primary.main,\n    color: 'white',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontSize: '0.875rem',\n    fontWeight: dattaAbleTheme.typography.fontWeight.medium,\n  };\n\n  const getUserInitials = (name: string | undefined): string => {\n    if (!name) return 'U';\n    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);\n  };\n\n  return (\n    <div style={window.innerWidth < 768 ? mobileHeaderStyles : headerStyles}>\n      <Navbar expand=\"lg\" style={navbarStyles} className=\"px-0\">\n        <div className=\"d-flex align-items-center\">\n          {/* Mobile Menu Toggle */}\n          <Button\n            style={toggleButtonStyles}\n            onClick={onToggleSidebar}\n            className=\"d-lg-none me-2\"\n            onMouseEnter={(e) => {\n              (e.target as HTMLElement).style.backgroundColor = dattaAbleTheme.colors.background.light;\n            }}\n            onMouseLeave={(e) => {\n              (e.target as HTMLElement).style.backgroundColor = 'transparent';\n            }}\n          >\n            <i className=\"fas fa-bars\"></i>\n          </Button>\n\n          {/* Desktop Sidebar Toggle */}\n          <Button\n            style={toggleButtonStyles}\n            onClick={onToggleSidebarCollapse}\n            className=\"d-none d-lg-block me-3\"\n            onMouseEnter={(e) => {\n              (e.target as HTMLElement).style.backgroundColor = dattaAbleTheme.colors.background.light;\n            }}\n            onMouseLeave={(e) => {\n              (e.target as HTMLElement).style.backgroundColor = 'transparent';\n            }}\n          >\n            <i className={`fas ${sidebarCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'}`}></i>\n          </Button>\n\n          {/* Page Title */}\n          <h5 className=\"mb-0 text-dark fw-semibold\">\n            Dashboard\n          </h5>\n        </div>\n\n        {/* Right Side Navigation */}\n        <Nav className=\"ms-auto d-flex align-items-center\">\n          {/* Notifications */}\n          <Dropdown className=\"me-3\">\n            <Dropdown.Toggle\n              as=\"button\"\n              style={{\n                ...toggleButtonStyles,\n                position: 'relative',\n              }}\n              className=\"position-relative\"\n            >\n              <i className=\"fas fa-bell\"></i>\n              <span\n                style={{\n                  position: 'absolute',\n                  top: '4px',\n                  right: '4px',\n                  width: '8px',\n                  height: '8px',\n                  backgroundColor: dattaAbleTheme.colors.error.main,\n                  borderRadius: '50%',\n                }}\n              ></span>\n            </Dropdown.Toggle>\n            <Dropdown.Menu align=\"end\" style={{ minWidth: '300px' }}>\n              <Dropdown.Header>Notifications</Dropdown.Header>\n              <Dropdown.Item>\n                <div className=\"d-flex\">\n                  <div className=\"flex-shrink-0\">\n                    <div\n                      style={{\n                        width: '32px',\n                        height: '32px',\n                        backgroundColor: dattaAbleTheme.colors.success.main,\n                        borderRadius: '50%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                      }}\n                    >\n                      <i className=\"fas fa-check text-white\"></i>\n                    </div>\n                  </div>\n                  <div className=\"flex-grow-1 ms-3\">\n                    <h6 className=\"mb-1\">Welcome to Dashboard</h6>\n                    <p className=\"mb-0 text-muted small\">\n                      Your dashboard is ready to use\n                    </p>\n                  </div>\n                </div>\n              </Dropdown.Item>\n              <Dropdown.Divider />\n              <Dropdown.Item className=\"text-center\">\n                <small>View all notifications</small>\n              </Dropdown.Item>\n            </Dropdown.Menu>\n          </Dropdown>\n\n          {/* User Profile Dropdown */}\n          <Dropdown show={showProfileDropdown} onToggle={setShowProfileDropdown}>\n            <Dropdown.Toggle\n              as=\"div\"\n              style={{ cursor: 'pointer' }}\n              className=\"d-flex align-items-center\"\n            >\n              <div style={userAvatarStyles}>\n                {getUserInitials(user?.name)}\n              </div>\n              <div className=\"ms-2 d-none d-sm-block\">\n                <div\n                  style={{\n                    fontSize: '0.875rem',\n                    fontWeight: dattaAbleTheme.typography.fontWeight.medium,\n                    color: dattaAbleTheme.colors.text.primary,\n                  }}\n                >\n                  {user?.name || 'User'}\n                </div>\n                <div\n                  style={{\n                    fontSize: '0.75rem',\n                    color: dattaAbleTheme.colors.text.secondary,\n                  }}\n                >\n                  {user?.email || '<EMAIL>'}\n                </div>\n              </div>\n              <i className=\"fas fa-chevron-down ms-2 text-muted\"></i>\n            </Dropdown.Toggle>\n\n            <Dropdown.Menu align=\"end\" style={{ minWidth: '200px' }}>\n              <Dropdown.Header>\n                <div className=\"text-center\">\n                  <div style={userAvatarStyles} className=\"mx-auto mb-2\">\n                    {getUserInitials(user?.name)}\n                  </div>\n                  <h6 className=\"mb-0\">{user?.name || 'User'}</h6>\n                  <small className=\"text-muted\">{user?.email || '<EMAIL>'}</small>\n                </div>\n              </Dropdown.Header>\n              <Dropdown.Divider />\n              <Dropdown.Item onClick={() => navigate('/profile')}>\n                <i className=\"fas fa-user me-2\"></i>\n                Profile\n              </Dropdown.Item>\n              <Dropdown.Item onClick={() => navigate('/profile/edit')}>\n                <i className=\"fas fa-cog me-2\"></i>\n                Settings\n              </Dropdown.Item>\n              {user?.role === 'admin' && (\n                <>\n                  <Dropdown.Divider />\n                  <Dropdown.Item onClick={handleAdminPanel}>\n                    <i className=\"fas fa-shield-alt me-2\"></i>\n                    Admin Panel\n                  </Dropdown.Item>\n                </>\n              )}\n              <Dropdown.Divider />\n              <Dropdown.Item onClick={handleLogout} className=\"text-danger\">\n                <i className=\"fas fa-sign-out-alt me-2\"></i>\n                Logout\n              </Dropdown.Item>\n            </Dropdown.Menu>\n          </Dropdown>\n        </Nav>\n      </Navbar>\n\n      {/* Font Awesome Icons */}\n      <link\n        rel=\"stylesheet\"\n        href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"\n      />\n\n      <style>{`\n        .dropdown-toggle::after {\n          display: none;\n        }\n\n        .dropdown-menu {\n          border: 1px solid ${dattaAbleTheme.colors.border};\n          border-radius: ${dattaAbleTheme.borderRadius.lg};\n          box-shadow: ${dattaAbleTheme.shadows.lg};\n          padding: ${dattaAbleTheme.spacing[2]};\n        }\n\n        .dropdown-item {\n          border-radius: ${dattaAbleTheme.borderRadius.md};\n          padding: ${dattaAbleTheme.spacing[2]} ${dattaAbleTheme.spacing[3]};\n          transition: all 0.2s ease;\n        }\n\n        .dropdown-item:hover {\n          background-color: ${dattaAbleTheme.colors.background.light};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .dropdown-header {\n          padding: ${dattaAbleTheme.spacing[3]};\n          font-weight: ${dattaAbleTheme.typography.fontWeight.semibold};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        @media (max-width: 767.98px) {\n          .header-mobile {\n            left: 0 !important;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default DattaAbleHeader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,iBAAiB;AAC/D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,cAAc,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQxD,MAAMC,eAA+C,GAAGA,CAAC;EAAEC,eAAe;EAAEC,uBAAuB;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EAC1H,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc,IAAI;IAAEC;EAAO,CAAC,GAAGd,OAAO,CAAC,CAAC;EAClC,MAAM,CAACe,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAMuB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMH,MAAM,CAAC,CAAC;IACdF,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMM,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMjB,WAAW,CAACkB,oBAAoB,CAAC,CAAC;IAC1C,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,qBAAA;MACnBC,OAAO,CAACF,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD;MACA,MAAMG,QAAQ,GAAG,EAAAF,qBAAA,GAAAG,OAAO,CAACC,GAAG,CAACC,iBAAiB,cAAAL,qBAAA,uBAA7BA,qBAAA,CAA+BM,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAI,6BAA6B;MAC1GC,MAAM,CAACC,IAAI,CAACN,QAAQ,EAAE,QAAQ,CAAC;IACjC;EACF,CAAC;EAED,MAAMO,YAAiC,GAAG;IACxCC,QAAQ,EAAE,OAAO;IACjBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,IAAI,EAAExB,gBAAgB,GAAGR,cAAc,CAACiC,MAAM,CAACC,OAAO,CAACC,cAAc,GAAGnC,cAAc,CAACiC,MAAM,CAACC,OAAO,CAACE,KAAK;IAC3GC,MAAM,EAAErC,cAAc,CAACiC,MAAM,CAACK,MAAM,CAACD,MAAM;IAC3CE,eAAe,EAAEvC,cAAc,CAACwC,MAAM,CAACC,UAAU,CAACC,KAAK;IACvDC,YAAY,EAAE,aAAa3C,cAAc,CAACwC,MAAM,CAACI,MAAM,EAAE;IACzDC,SAAS,EAAE7C,cAAc,CAAC8C,OAAO,CAACC,EAAE;IACpCC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,gBAAgB;IAC5BC,OAAO,EAAE;EACX,CAAC;EAED,MAAMC,kBAAuC,GAAG;IAC9C,GAAGvB,YAAY;IACfI,IAAI,EAAE;EACR,CAAC;EAED,MAAMoB,YAAiC,GAAG;IACxCf,MAAM,EAAE,MAAM;IACda,OAAO,EAAE,KAAKlD,cAAc,CAACqD,OAAO,CAAC,CAAC,CAAC;EACzC,CAAC;EAED,MAAMC,kBAAuC,GAAG;IAC9Cf,eAAe,EAAE,aAAa;IAC9BK,MAAM,EAAE,MAAM;IACdW,KAAK,EAAEvD,cAAc,CAACwC,MAAM,CAACgB,IAAI,CAACC,OAAO;IACzCC,QAAQ,EAAE,SAAS;IACnBR,OAAO,EAAElD,cAAc,CAACqD,OAAO,CAAC,CAAC,CAAC;IAClCM,YAAY,EAAE3D,cAAc,CAAC2D,YAAY,CAACC,EAAE;IAC5CX,UAAU,EAAE;EACd,CAAC;EAED,MAAMY,gBAAqC,GAAG;IAC5CzB,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdsB,YAAY,EAAE,KAAK;IACnBpB,eAAe,EAAEvC,cAAc,CAACwC,MAAM,CAACiB,OAAO,CAACK,IAAI;IACnDP,KAAK,EAAE,OAAO;IACdQ,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBP,QAAQ,EAAE,UAAU;IACpBQ,UAAU,EAAElE,cAAc,CAACmE,UAAU,CAACD,UAAU,CAACE;EACnD,CAAC;EAED,MAAMC,eAAe,GAAIC,IAAwB,IAAa;IAC5D,IAAI,CAACA,IAAI,EAAE,OAAO,GAAG;IACrB,OAAOA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1E,CAAC;EAED,oBACE1E,OAAA;IAAK2E,KAAK,EAAEnD,MAAM,CAACoD,UAAU,GAAG,GAAG,GAAG3B,kBAAkB,GAAGvB,YAAa;IAAAmD,QAAA,gBACtE7E,OAAA,CAACT,MAAM;MAACuF,MAAM,EAAC,IAAI;MAACH,KAAK,EAAEzB,YAAa;MAAC6B,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACvD7E,OAAA;QAAK+E,SAAS,EAAC,2BAA2B;QAAAF,QAAA,gBAExC7E,OAAA,CAACN,MAAM;UACLiF,KAAK,EAAEvB,kBAAmB;UAC1B4B,OAAO,EAAE5E,eAAgB;UACzB2E,SAAS,EAAC,gBAAgB;UAC1BE,YAAY,EAAGC,CAAC,IAAK;YAClBA,CAAC,CAACC,MAAM,CAAiBR,KAAK,CAACtC,eAAe,GAAGvC,cAAc,CAACwC,MAAM,CAACC,UAAU,CAAC6C,KAAK;UAC1F,CAAE;UACFC,YAAY,EAAGH,CAAC,IAAK;YAClBA,CAAC,CAACC,MAAM,CAAiBR,KAAK,CAACtC,eAAe,GAAG,aAAa;UACjE,CAAE;UAAAwC,QAAA,eAEF7E,OAAA;YAAG+E,SAAS,EAAC;UAAa;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAGTzF,OAAA,CAACN,MAAM;UACLiF,KAAK,EAAEvB,kBAAmB;UAC1B4B,OAAO,EAAE3E,uBAAwB;UACjC0E,SAAS,EAAC,wBAAwB;UAClCE,YAAY,EAAGC,CAAC,IAAK;YAClBA,CAAC,CAACC,MAAM,CAAiBR,KAAK,CAACtC,eAAe,GAAGvC,cAAc,CAACwC,MAAM,CAACC,UAAU,CAAC6C,KAAK;UAC1F,CAAE;UACFC,YAAY,EAAGH,CAAC,IAAK;YAClBA,CAAC,CAACC,MAAM,CAAiBR,KAAK,CAACtC,eAAe,GAAG,aAAa;UACjE,CAAE;UAAAwC,QAAA,eAEF7E,OAAA;YAAG+E,SAAS,EAAE,OAAOzE,gBAAgB,GAAG,kBAAkB,GAAG,iBAAiB;UAAG;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eAGTzF,OAAA;UAAI+E,SAAS,EAAC,4BAA4B;UAAAF,QAAA,EAAC;QAE3C;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGNzF,OAAA,CAACR,GAAG;QAACuF,SAAS,EAAC,mCAAmC;QAAAF,QAAA,gBAEhD7E,OAAA,CAACP,QAAQ;UAACsF,SAAS,EAAC,MAAM;UAAAF,QAAA,gBACxB7E,OAAA,CAACP,QAAQ,CAACiG,MAAM;YACdC,EAAE,EAAC,QAAQ;YACXhB,KAAK,EAAE;cACL,GAAGvB,kBAAkB;cACrBzB,QAAQ,EAAE;YACZ,CAAE;YACFoD,SAAS,EAAC,mBAAmB;YAAAF,QAAA,gBAE7B7E,OAAA;cAAG+E,SAAS,EAAC;YAAa;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BzF,OAAA;cACE2E,KAAK,EAAE;gBACLhD,QAAQ,EAAE,UAAU;gBACpBC,GAAG,EAAE,KAAK;gBACVC,KAAK,EAAE,KAAK;gBACZK,KAAK,EAAE,KAAK;gBACZC,MAAM,EAAE,KAAK;gBACbE,eAAe,EAAEvC,cAAc,CAACwC,MAAM,CAACtB,KAAK,CAAC4C,IAAI;gBACjDH,YAAY,EAAE;cAChB;YAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAClBzF,OAAA,CAACP,QAAQ,CAACmG,IAAI;YAACC,KAAK,EAAC,KAAK;YAAClB,KAAK,EAAE;cAAEmB,QAAQ,EAAE;YAAQ,CAAE;YAAAjB,QAAA,gBACtD7E,OAAA,CAACP,QAAQ,CAACsG,MAAM;cAAAlB,QAAA,EAAC;YAAa;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,eAChDzF,OAAA,CAACP,QAAQ,CAACuG,IAAI;cAAAnB,QAAA,eACZ7E,OAAA;gBAAK+E,SAAS,EAAC,QAAQ;gBAAAF,QAAA,gBACrB7E,OAAA;kBAAK+E,SAAS,EAAC,eAAe;kBAAAF,QAAA,eAC5B7E,OAAA;oBACE2E,KAAK,EAAE;sBACLzC,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdE,eAAe,EAAEvC,cAAc,CAACwC,MAAM,CAAC2D,OAAO,CAACrC,IAAI;sBACnDH,YAAY,EAAE,KAAK;sBACnBI,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE;oBAClB,CAAE;oBAAAc,QAAA,eAEF7E,OAAA;sBAAG+E,SAAS,EAAC;oBAAyB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzF,OAAA;kBAAK+E,SAAS,EAAC,kBAAkB;kBAAAF,QAAA,gBAC/B7E,OAAA;oBAAI+E,SAAS,EAAC,MAAM;oBAAAF,QAAA,EAAC;kBAAoB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9CzF,OAAA;oBAAG+E,SAAS,EAAC,uBAAuB;oBAAAF,QAAA,EAAC;kBAErC;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAChBzF,OAAA,CAACP,QAAQ,CAACyG,OAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpBzF,OAAA,CAACP,QAAQ,CAACuG,IAAI;cAACjB,SAAS,EAAC,aAAa;cAAAF,QAAA,eACpC7E,OAAA;gBAAA6E,QAAA,EAAO;cAAsB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGXzF,OAAA,CAACP,QAAQ;UAAC0G,IAAI,EAAExF,mBAAoB;UAACyF,QAAQ,EAAExF,sBAAuB;UAAAiE,QAAA,gBACpE7E,OAAA,CAACP,QAAQ,CAACiG,MAAM;YACdC,EAAE,EAAC,KAAK;YACRhB,KAAK,EAAE;cAAE0B,MAAM,EAAE;YAAU,CAAE;YAC7BtB,SAAS,EAAC,2BAA2B;YAAAF,QAAA,gBAErC7E,OAAA;cAAK2E,KAAK,EAAEhB,gBAAiB;cAAAkB,QAAA,EAC1BV,eAAe,CAAC1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,IAAI;YAAC;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACNzF,OAAA;cAAK+E,SAAS,EAAC,wBAAwB;cAAAF,QAAA,gBACrC7E,OAAA;gBACE2E,KAAK,EAAE;kBACLnB,QAAQ,EAAE,UAAU;kBACpBQ,UAAU,EAAElE,cAAc,CAACmE,UAAU,CAACD,UAAU,CAACE,MAAM;kBACvDb,KAAK,EAAEvD,cAAc,CAACwC,MAAM,CAACgB,IAAI,CAACC;gBACpC,CAAE;gBAAAsB,QAAA,EAED,CAAApE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,IAAI,KAAI;cAAM;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACNzF,OAAA;gBACE2E,KAAK,EAAE;kBACLnB,QAAQ,EAAE,SAAS;kBACnBH,KAAK,EAAEvD,cAAc,CAACwC,MAAM,CAACgB,IAAI,CAACgD;gBACpC,CAAE;gBAAAzB,QAAA,EAED,CAAApE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8F,KAAK,KAAI;cAAkB;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNzF,OAAA;cAAG+E,SAAS,EAAC;YAAqC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eAElBzF,OAAA,CAACP,QAAQ,CAACmG,IAAI;YAACC,KAAK,EAAC,KAAK;YAAClB,KAAK,EAAE;cAAEmB,QAAQ,EAAE;YAAQ,CAAE;YAAAjB,QAAA,gBACtD7E,OAAA,CAACP,QAAQ,CAACsG,MAAM;cAAAlB,QAAA,eACd7E,OAAA;gBAAK+E,SAAS,EAAC,aAAa;gBAAAF,QAAA,gBAC1B7E,OAAA;kBAAK2E,KAAK,EAAEhB,gBAAiB;kBAACoB,SAAS,EAAC,cAAc;kBAAAF,QAAA,EACnDV,eAAe,CAAC1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,IAAI;gBAAC;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNzF,OAAA;kBAAI+E,SAAS,EAAC,MAAM;kBAAAF,QAAA,EAAE,CAAApE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,IAAI,KAAI;gBAAM;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChDzF,OAAA;kBAAO+E,SAAS,EAAC,YAAY;kBAAAF,QAAA,EAAE,CAAApE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8F,KAAK,KAAI;gBAAkB;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eAClBzF,OAAA,CAACP,QAAQ,CAACyG,OAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpBzF,OAAA,CAACP,QAAQ,CAACuG,IAAI;cAAChB,OAAO,EAAEA,CAAA,KAAMxE,QAAQ,CAAC,UAAU,CAAE;cAAAqE,QAAA,gBACjD7E,OAAA;gBAAG+E,SAAS,EAAC;cAAkB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,WAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChBzF,OAAA,CAACP,QAAQ,CAACuG,IAAI;cAAChB,OAAO,EAAEA,CAAA,KAAMxE,QAAQ,CAAC,eAAe,CAAE;cAAAqE,QAAA,gBACtD7E,OAAA;gBAAG+E,SAAS,EAAC;cAAiB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,YAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,EACf,CAAAhF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+F,IAAI,MAAK,OAAO,iBACrBxG,OAAA,CAAAE,SAAA;cAAA2E,QAAA,gBACE7E,OAAA,CAACP,QAAQ,CAACyG,OAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpBzF,OAAA,CAACP,QAAQ,CAACuG,IAAI;gBAAChB,OAAO,EAAElE,gBAAiB;gBAAA+D,QAAA,gBACvC7E,OAAA;kBAAG+E,SAAS,EAAC;gBAAwB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAE5C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA,eAChB,CACH,eACDzF,OAAA,CAACP,QAAQ,CAACyG,OAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpBzF,OAAA,CAACP,QAAQ,CAACuG,IAAI;cAAChB,OAAO,EAAEnE,YAAa;cAACkE,SAAS,EAAC,aAAa;cAAAF,QAAA,gBAC3D7E,OAAA;gBAAG+E,SAAS,EAAC;cAA0B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,UAE9C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTzF,OAAA;MACEyG,GAAG,EAAC,YAAY;MAChBC,IAAI,EAAC;IAA2E;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjF,CAAC,eAEFzF,OAAA;MAAA6E,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA,8BAA8B/E,cAAc,CAACwC,MAAM,CAACI,MAAM;AAC1D,2BAA2B5C,cAAc,CAAC2D,YAAY,CAACkD,EAAE;AACzD,wBAAwB7G,cAAc,CAAC8C,OAAO,CAAC+D,EAAE;AACjD,qBAAqB7G,cAAc,CAACqD,OAAO,CAAC,CAAC,CAAC;AAC9C;AACA;AACA;AACA,2BAA2BrD,cAAc,CAAC2D,YAAY,CAACC,EAAE;AACzD,qBAAqB5D,cAAc,CAACqD,OAAO,CAAC,CAAC,CAAC,IAAIrD,cAAc,CAACqD,OAAO,CAAC,CAAC,CAAC;AAC3E;AACA;AACA;AACA;AACA,8BAA8BrD,cAAc,CAACwC,MAAM,CAACC,UAAU,CAAC6C,KAAK;AACpE,mBAAmBtF,cAAc,CAACwC,MAAM,CAACgB,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,qBAAqBzD,cAAc,CAACqD,OAAO,CAAC,CAAC,CAAC;AAC9C,yBAAyBrD,cAAc,CAACmE,UAAU,CAACD,UAAU,CAAC4C,QAAQ;AACtE,mBAAmB9G,cAAc,CAACwC,MAAM,CAACgB,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAClF,EAAA,CA7RIJ,eAA+C;EAAA,QAClCR,WAAW,EACHC,OAAO;AAAA;AAAAiH,EAAA,GAF5B1G,eAA+C;AA+RrD,eAAeA,eAAe;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}