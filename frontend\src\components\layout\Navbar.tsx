import React from 'react';
import { Navbar as BootstrapNavbar, Nav, NavDropdown, Container, Button } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import authService from '../../services/authService';

const Navbar: React.FC = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleAdminPanel = async () => {
    try {
      await authService.navigateToAdminPanel();
    } catch (error: any) {
      console.error('Failed to access admin panel:', error);
      // Fallback to direct navigation (user will need to login manually)
      const baseUrl = process.env.REACT_APP_API_URL?.replace('/api', '') || 'http://localhost:8000';
      const adminUrl = `${baseUrl}/admin`;
      window.open(adminUrl, '_blank');
    }
  };

  return (
    <BootstrapNavbar bg="dark" variant="dark" expand="lg" sticky="top">
      <Container>
        <BootstrapNavbar.Brand as={Link} to="/" className="text-decoration-none">
          Full Stack CMS
        </BootstrapNavbar.Brand>

        <BootstrapNavbar.Toggle aria-controls="basic-navbar-nav" />
        <BootstrapNavbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">
            <Nav.Link as={Link} to="/" className="text-decoration-none">
              Home
            </Nav.Link>
          </Nav>

          <Nav className="ms-auto">
            {isAuthenticated ? (
              <>
                <Nav.Link as={Link} to="/dashboard" className="text-decoration-none">
                  Dashboard
                </Nav.Link>
                <NavDropdown
                  title={user?.name || 'User'}
                  id="user-dropdown"
                  align="end"
                >
                  <NavDropdown.Item onClick={() => navigate('/profile')}>
                    Profile
                  </NavDropdown.Item>
                  <NavDropdown.Item onClick={() => navigate('/profile/edit')}>
                    Edit Profile
                  </NavDropdown.Item>
                  {user?.role === 'admin' && (
                    <>
                      <NavDropdown.Divider />
                      <NavDropdown.Item onClick={handleAdminPanel}>
                        Admin Panel
                      </NavDropdown.Item>
                    </>
                  )}
                  <NavDropdown.Divider />
                  <NavDropdown.Item onClick={handleLogout}>
                    Logout
                  </NavDropdown.Item>
                </NavDropdown>

                {!user?.email_verified_at && (
                  <Button
                    variant="outline-warning"
                    size="sm"
                    className="ms-2"
                    onClick={() => navigate('/email-verification')}
                  >
                    Verify Email
                  </Button>
                )}
              </>
            ) : (
              <>
                <Nav.Link as={Link} to="/login" className="text-decoration-none">
                  Login
                </Nav.Link>
                <Button
                  variant="primary"
                  className="ms-2"
                  onClick={() => navigate('/register')}
                >
                  Register
                </Button>
              </>
            )}
          </Nav>
        </BootstrapNavbar.Collapse>
      </Container>
    </BootstrapNavbar>
  );
};

export default Navbar;
