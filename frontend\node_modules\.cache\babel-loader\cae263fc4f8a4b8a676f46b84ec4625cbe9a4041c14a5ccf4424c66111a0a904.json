{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Typography,Stepper,Step,StepLabel,Button,Paper,Card,CardContent,CardMedia,Chip,Alert,TextField,Divider}from'@mui/material';import{useNavigate}from'react-router-dom';import printingService from'../../services/printingService';import FileUpload from'../../components/dashboard/FileUpload';import ErrorBoundary from'../../components/common/ErrorBoundary';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const steps=['Select Category','Choose Product','Configure Order','Upload Files','Review & Submit'];// Helper function to safely convert values to numbers for display\nconst safeNumber=value=>{const num=Number(value);return isNaN(num)?0:num;};const Order=()=>{const navigate=useNavigate();const[activeStep,setActiveStep]=useState(0);const[categories,setCategories]=useState([]);const[products,setProducts]=useState([]);const[selectedCategory,setSelectedCategory]=useState(null);const[selectedProduct,setSelectedProduct]=useState(null);const[orderItems,setOrderItems]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);// Product configuration state\nconst[quantity,setQuantity]=useState(1);const[selectedOptions,setSelectedOptions]=useState({});const[priceCalculation,setPriceCalculation]=useState(null);const[calculatingPrice,setCalculatingPrice]=useState(false);// Order and file upload state\nconst[createdOrder,setCreatedOrder]=useState(null);const[uploadedFiles,setUploadedFiles]=useState([]);const[submittingOrder,setSubmittingOrder]=useState(false);useEffect(()=>{loadCategories();},[]);const loadCategories=async()=>{try{setLoading(true);const data=await printingService.getCategories();setCategories(data);}catch(err){setError('Failed to load categories');}finally{setLoading(false);}};const loadProducts=async categorySlug=>{try{setLoading(true);const data=await printingService.getProducts(categorySlug);setProducts(data);}catch(err){setError('Failed to load products');}finally{setLoading(false);}};const handleCategorySelect=async category=>{setSelectedCategory(category);await loadProducts(category.slug);setActiveStep(1);};const handleProductSelect=product=>{setSelectedProduct(product);// Reset configuration when selecting a new product\n// Ensure we use the product's actual minimum quantity, with a fallback to 1\nconst initialQuantity=product.min_quantity&&product.min_quantity>0?product.min_quantity:1;setQuantity(initialQuantity);setSelectedOptions({});setPriceCalculation(null);setActiveStep(2);};// Calculate price when quantity or options change\nconst calculatePrice=async()=>{if(!selectedProduct)return;// Convert quantity to number for API call\nconst numericQuantity=typeof quantity==='string'?parseInt(quantity,10):quantity;const minQty=selectedProduct.min_quantity&&selectedProduct.min_quantity>0?selectedProduct.min_quantity:1;if(isNaN(numericQuantity)||numericQuantity<minQty)return;setCalculatingPrice(true);try{const result=await printingService.calculatePrice(selectedProduct.id,numericQuantity,selectedOptions);setPriceCalculation(result);}catch(err){setError('Failed to calculate price');console.error('Price calculation error:',err);}finally{setCalculatingPrice(false);}};// Effect to calculate price when quantity or options change\nuseEffect(()=>{const numericQuantity=typeof quantity==='string'?parseInt(quantity,10):quantity;const minQty=selectedProduct!==null&&selectedProduct!==void 0&&selectedProduct.min_quantity&&selectedProduct.min_quantity>0?selectedProduct.min_quantity:1;if(selectedProduct&&!isNaN(numericQuantity)&&numericQuantity>=minQty){const timeoutId=setTimeout(()=>{calculatePrice();},300);// Debounce price calculation\nreturn()=>clearTimeout(timeoutId);}else{setPriceCalculation(null);}},[selectedProduct,quantity,selectedOptions]);const handleNext=async()=>{// If moving from configuration step, save the order item and create order\nif(activeStep===2&&selectedProduct&&priceCalculation){const numericQuantity=typeof quantity==='string'?parseInt(quantity,10):quantity;const orderItem={product_id:selectedProduct.id,quantity:numericQuantity,selected_options:selectedOptions,specifications:selectedProduct.specifications};setOrderItems([orderItem]);// Create the order when moving to file upload step\nawait createOrder([orderItem]);}setActiveStep(prevActiveStep=>prevActiveStep+1);};const handleBack=()=>{setActiveStep(prevActiveStep=>prevActiveStep-1);};const createOrder=async items=>{try{setLoading(true);const orderData={items,special_instructions:'',delivery_method:'standard'};console.log('Creating order with data:',orderData);const order=await printingService.createOrder(orderData);setCreatedOrder(order);console.log('Order created successfully:',order);}catch(err){var _err$response,_err$response2,_err$response2$data,_err$response3,_err$response3$data;console.error('Order creation error details:',err);console.error('Error response:',(_err$response=err.response)===null||_err$response===void 0?void 0:_err$response.data);let errorMessage='Failed to create order';if((_err$response2=err.response)!==null&&_err$response2!==void 0&&(_err$response2$data=_err$response2.data)!==null&&_err$response2$data!==void 0&&_err$response2$data.message){errorMessage=err.response.data.message;}else if((_err$response3=err.response)!==null&&_err$response3!==void 0&&(_err$response3$data=_err$response3.data)!==null&&_err$response3$data!==void 0&&_err$response3$data.errors){const errors=Object.values(err.response.data.errors).flat();errorMessage=errors.join(', ');}else if(err.message){errorMessage=err.message;}setError(errorMessage);}finally{setLoading(false);}};const handleFilesUploaded=files=>{setUploadedFiles(prev=>[...prev,...files]);};const handleFileUploadError=error=>{setError(error);};const handleSubmitOrder=async()=>{if(!createdOrder){setError('No order to submit');return;}try{setSubmittingOrder(true);// Order is already created, just navigate to success or orders page\nnavigate('/dashboard/orders');}catch(err){setError(err.message||'Failed to submit order');}finally{setSubmittingOrder(false);}};const renderStepContent=step=>{var _selectedProduct$opti;switch(step){case 0:return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Select a Printing Category\"}),/*#__PURE__*/_jsx(Box,{sx:{display:'grid',gridTemplateColumns:{xs:'1fr',sm:'repeat(2, 1fr)',md:'repeat(3, 1fr)'},gap:3},children:categories.map(category=>/*#__PURE__*/_jsxs(Card,{sx:{cursor:'pointer','&:hover':{elevation:4}},onClick:()=>handleCategorySelect(category),children:[category.image&&/*#__PURE__*/_jsx(CardMedia,{component:\"img\",height:\"140\",image:category.image,alt:category.name}),/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{gutterBottom:true,variant:\"h6\",component:\"div\",children:category.name}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:category.description}),category.products_count!==undefined&&/*#__PURE__*/_jsx(Chip,{label:`${category.products_count} products`,size:\"small\",sx:{mt:1}})]})]},category.id))})]});case 1:return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,children:[\"Choose a Product from \",selectedCategory===null||selectedCategory===void 0?void 0:selectedCategory.name]}),/*#__PURE__*/_jsx(Box,{sx:{display:'grid',gridTemplateColumns:{xs:'1fr',sm:'repeat(2, 1fr)',md:'repeat(3, 1fr)'},gap:3},children:products.map(product=>/*#__PURE__*/_jsxs(Card,{sx:{cursor:'pointer','&:hover':{elevation:4}},onClick:()=>handleProductSelect(product),children:[product.image&&/*#__PURE__*/_jsx(CardMedia,{component:\"img\",height:\"140\",image:product.image,alt:product.name}),/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{gutterBottom:true,variant:\"h6\",component:\"div\",children:product.name}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:2},children:product.description}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"primary\",children:product.formatted_base_price}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",display:\"block\",children:[\"Min: \",product.min_quantity,\" | Production: \",product.production_time_days,\" days\"]})]})]},product.id))})]});case 2:return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Configure Your Order\"}),selectedProduct&&/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:{xs:'column',md:'row'},gap:3},children:[/*#__PURE__*/_jsx(Box,{sx:{flex:1},children:/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:selectedProduct.name}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:2},children:selectedProduct.description}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",gutterBottom:true,children:\"Specifications:\"}),/*#__PURE__*/_jsx(Box,{sx:{mb:2},children:Object.entries(selectedProduct.specifications||{}).map(_ref=>{let[key,value]=_ref;return/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{mb:0.5},children:[/*#__PURE__*/_jsxs(\"strong\",{children:[key,\":\"]}),\" \",value]},key);})}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",children:[\"Production Time: \",selectedProduct.production_time_days,\" days\"]})]})}),/*#__PURE__*/_jsx(Box,{sx:{flex:1},children:/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Order Configuration\"}),/*#__PURE__*/_jsx(TextField,{label:\"Quantity\",type:\"text\",value:quantity,onChange:e=>{const inputValue=e.target.value;// Allow empty input for better user experience while typing\nif(inputValue===''){setQuantity('');return;}// Only allow numeric characters\nif(!/^\\d+$/.test(inputValue)){return;}const numericValue=parseInt(inputValue,10);const maxQty=selectedProduct.max_quantity;// Only enforce maximum constraint during typing, not minimum\n// This allows users to type freely above minimum quantity\nif(maxQty&&numericValue>maxQty){setQuantity(maxQty);}else{setQuantity(numericValue);}},onBlur:e=>{// Apply full validation when user leaves the field\nconst currentValue=e.target.value;const minQty=selectedProduct.min_quantity&&selectedProduct.min_quantity>0?selectedProduct.min_quantity:1;const maxQty=selectedProduct.max_quantity;if(currentValue===''||isNaN(parseInt(currentValue,10))){// If empty or invalid, set to minimum quantity\nsetQuantity(minQty);}else{// Apply min/max constraints\nconst numericValue=parseInt(currentValue,10);let validatedQuantity=Math.max(minQty,numericValue);if(maxQty&&validatedQuantity>maxQty){validatedQuantity=maxQty;}setQuantity(validatedQuantity);}},onKeyDown:e=>{// Handle Enter key to apply validation immediately\nif(e.key==='Enter'){e.currentTarget.blur();// Trigger onBlur validation\nreturn;}// Allow navigation keys, backspace, delete, tab, escape\nif(['ArrowLeft','ArrowRight','ArrowUp','ArrowDown','Backspace','Delete','Tab','Escape','Home','End'].includes(e.key)){return;}// Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z\nif(e.ctrlKey&&['a','c','v','x','z'].includes(e.key.toLowerCase())){return;}// Only allow numeric characters\nif(!/^\\d$/.test(e.key)){e.preventDefault();}},slotProps:{htmlInput:{inputMode:'numeric',pattern:'[0-9]*',autoComplete:'off',style:{fontSize:'16px',// Prevents zoom on mobile devices\ntextAlign:'left'}}},helperText:`Min: ${selectedProduct.min_quantity&&selectedProduct.min_quantity>0?selectedProduct.min_quantity:1}${selectedProduct.max_quantity?`, Max: ${selectedProduct.max_quantity}`:''} • Type quantity directly`,fullWidth:true,sx:{mb:3,'& .MuiOutlinedInput-root':{'&:hover fieldset':{borderColor:'primary.main'},'&.Mui-focused fieldset':{borderColor:'primary.main'}}}}),((_selectedProduct$opti=selectedProduct.options)===null||_selectedProduct$opti===void 0?void 0:_selectedProduct$opti.quantity_pricing)&&/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Pricing Tiers:\"}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexDirection:'column',gap:1},children:selectedProduct.options.quantity_pricing.map((tier,index)=>{var _selectedProduct$opti2;const numericQuantity=typeof quantity==='string'?parseInt(quantity,10):quantity;const isCurrentTier=!isNaN(numericQuantity)&&numericQuantity>=tier.min_quantity&&(index===selectedProduct.options.quantity_pricing.length-1||numericQuantity<((_selectedProduct$opti2=selectedProduct.options.quantity_pricing[index+1])===null||_selectedProduct$opti2===void 0?void 0:_selectedProduct$opti2.min_quantity));return/*#__PURE__*/_jsx(Box,{sx:{p:1.5,borderRadius:1,border:'1px solid',borderColor:isCurrentTier?'primary.main':'grey.300',backgroundColor:isCurrentTier?'primary.50':'transparent',transition:'all 0.2s ease-in-out'},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{fontWeight:isCurrentTier?600:400,color:isCurrentTier?'primary.main':'text.primary'},children:[tier.min_quantity,\"+ units: RM \",safeNumber(tier.price_per_unit).toFixed(2),\" each\",isCurrentTier&&/*#__PURE__*/_jsx(Chip,{label:\"Current\",size:\"small\",color:\"primary\",sx:{ml:1,height:20}})]})},index);})})]}),/*#__PURE__*/_jsx(Divider,{sx:{my:2}}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Price Summary\"}),calculatingPrice?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Calculating...\"})}):priceCalculation?/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",children:[\"Unit Price: RM \",safeNumber(priceCalculation.unit_price).toFixed(2)]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",children:[\"Quantity: \",typeof quantity==='string'?parseInt(quantity,10)||0:quantity]}),/*#__PURE__*/_jsxs(Typography,{variant:\"h5\",color:\"primary\",sx:{mt:1},children:[\"Total: \",priceCalculation.formatted_total_price]}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",sx:{mt:1,display:'block'},children:[\"Estimated production time: \",priceCalculation.production_time_days,\" days\"]})]}):(()=>{const numericQuantity=typeof quantity==='string'?parseInt(quantity,10):quantity;const minQty=selectedProduct!==null&&selectedProduct!==void 0&&selectedProduct.min_quantity&&selectedProduct.min_quantity>0?selectedProduct.min_quantity:1;return!isNaN(numericQuantity)&&numericQuantity>=minQty;})()?/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Loading pricing...\"}):/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"error\",children:[\"Please enter a valid quantity (minimum: \",selectedProduct!==null&&selectedProduct!==void 0&&selectedProduct.min_quantity&&selectedProduct.min_quantity>0?selectedProduct.min_quantity:1,\")\"]})]})]})})]})]});case 3:return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Upload Artwork Files\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:3},children:\"Upload your artwork files for printing. You can upload multiple files and specify the file type for each upload.\"}),createdOrder?/*#__PURE__*/_jsx(ErrorBoundary,{fallback:/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:\"Failed to load file upload component. Please refresh the page and try again.\"}),children:/*#__PURE__*/_jsx(FileUpload,{orderId:createdOrder.id,onFilesUploaded:handleFilesUploaded,onError:handleFileUploadError})}):/*#__PURE__*/_jsx(Alert,{severity:\"warning\",sx:{mb:2},children:\"Creating order... Please wait.\"})]});case 4:return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Review Your Order\"}),createdOrder&&/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:{xs:'1fr',md:'1fr 1fr'},gap:3},children:[/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Order Summary\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:[\"Order #\",createdOrder.order_number]}),selectedProduct&&/*#__PURE__*/_jsxs(Box,{sx:{mt:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",children:\"Product:\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:selectedProduct.name}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",sx:{mt:1},children:\"Quantity:\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:typeof quantity==='string'?parseInt(quantity,10)||0:quantity}),priceCalculation&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",sx:{mt:1},children:\"Total Price:\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"primary\",children:priceCalculation.formatted_total_price})]}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",sx:{mt:1},children:\"Status:\"}),/*#__PURE__*/_jsx(Chip,{label:createdOrder.status||'Pending',color:\"warning\",size:\"small\"})]})]}),/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,children:[\"Uploaded Files (\",uploadedFiles.length,\")\"]}),uploadedFiles.length>0?/*#__PURE__*/_jsx(Box,{children:uploadedFiles.map((file,index)=>/*#__PURE__*/_jsxs(Box,{sx:{mb:1,p:1,bgcolor:'grey.50',borderRadius:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",noWrap:true,children:file.original_name}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[file.formatted_file_size,\" \\u2022 \",file.file_type_label]})]},file.id))}):/*#__PURE__*/_jsx(Alert,{severity:\"warning\",children:\"No files uploaded yet. You can still submit the order and upload files later.\"})]}),/*#__PURE__*/_jsx(Box,{sx:{gridColumn:'1 / -1',textAlign:'center',mt:2},children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",size:\"large\",onClick:handleSubmitOrder,disabled:submittingOrder,children:submittingOrder?'Submitting...':'Submit Order'})})]})]});default:return'Unknown step';}};if(loading){return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"400px\",children:/*#__PURE__*/_jsx(Typography,{children:\"Loading...\"})});}return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,children:\"Create New Order\"}),error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:error}),/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Stepper,{activeStep:activeStep,sx:{mb:4},children:steps.map(label=>/*#__PURE__*/_jsx(Step,{children:/*#__PURE__*/_jsx(StepLabel,{children:label})},label))}),/*#__PURE__*/_jsx(Box,{sx:{minHeight:400},children:renderStepContent(activeStep)}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'row',pt:2},children:[/*#__PURE__*/_jsx(Button,{color:\"inherit\",disabled:activeStep===0,onClick:handleBack,sx:{mr:1},children:\"Back\"}),/*#__PURE__*/_jsx(Box,{sx:{flex:'1 1 auto'}}),activeStep<steps.length-1&&/*#__PURE__*/_jsx(Button,{onClick:handleNext,disabled:loading||activeStep===0&&!selectedCategory||activeStep===1&&!selectedProduct||activeStep===2&&(()=>{const numericQuantity=typeof quantity==='string'?parseInt(quantity,10):quantity;const minQty=selectedProduct!==null&&selectedProduct!==void 0&&selectedProduct.min_quantity&&selectedProduct.min_quantity>0?selectedProduct.min_quantity:1;return!priceCalculation||isNaN(numericQuantity)||numericQuantity<minQty;})()||activeStep===3&&!createdOrder,children:loading?'Creating Order...':'Next'}),activeStep===steps.length-1&&/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",onClick:handleSubmitOrder,disabled:submittingOrder||!createdOrder,children:submittingOrder?'Submitting...':'Complete Order'})]})]})]});};export default Order;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Paper", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "Chip", "<PERSON><PERSON>", "TextField", "Divider", "useNavigate", "printingService", "FileUpload", "Error<PERSON>ou<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "steps", "safeNumber", "value", "num", "Number", "isNaN", "Order", "navigate", "activeStep", "setActiveStep", "categories", "setCategories", "products", "setProducts", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedProduct", "setSelectedProduct", "orderItems", "setOrderItems", "loading", "setLoading", "error", "setError", "quantity", "setQuantity", "selectedOptions", "setSelectedOptions", "priceCalculation", "setPriceCalculation", "calculatingPrice", "setCalculatingPrice", "createdOrder", "setCreated<PERSON><PERSON>r", "uploadedFiles", "setUploadedFiles", "submittingOrder", "setSubmittingOrder", "loadCategories", "data", "getCategories", "err", "loadProducts", "categorySlug", "getProducts", "handleCategorySelect", "category", "slug", "handleProductSelect", "product", "initialQuantity", "min_quantity", "calculatePrice", "numericQuantity", "parseInt", "min<PERSON>ty", "result", "id", "console", "timeoutId", "setTimeout", "clearTimeout", "handleNext", "orderItem", "product_id", "selected_options", "specifications", "createOrder", "prevActiveStep", "handleBack", "items", "orderData", "special_instructions", "delivery_method", "log", "order", "_err$response", "_err$response2", "_err$response2$data", "_err$response3", "_err$response3$data", "response", "errorMessage", "message", "errors", "Object", "values", "flat", "join", "handleFilesUploaded", "files", "prev", "handleFileUploadError", "handleSubmitOrder", "renderStepContent", "step", "_selectedProduct$opti", "children", "variant", "gutterBottom", "sx", "display", "gridTemplateColumns", "xs", "sm", "md", "gap", "map", "cursor", "elevation", "onClick", "image", "component", "height", "alt", "name", "color", "description", "products_count", "undefined", "label", "size", "mt", "mb", "formatted_base_price", "production_time_days", "flexDirection", "flex", "p", "entries", "_ref", "key", "type", "onChange", "e", "inputValue", "target", "test", "numericValue", "max<PERSON>ty", "max_quantity", "onBlur", "currentValue", "validatedQuantity", "Math", "max", "onKeyDown", "currentTarget", "blur", "includes", "ctrl<PERSON>ey", "toLowerCase", "preventDefault", "slotProps", "htmlInput", "inputMode", "pattern", "autoComplete", "style", "fontSize", "textAlign", "helperText", "fullWidth", "borderColor", "options", "quantity_pricing", "tier", "index", "_selectedProduct$opti2", "isCurrentTier", "length", "borderRadius", "border", "backgroundColor", "transition", "fontWeight", "price_per_unit", "toFixed", "ml", "my", "alignItems", "unit_price", "formatted_total_price", "fallback", "severity", "orderId", "onFilesUploaded", "onError", "order_number", "status", "file", "bgcolor", "noWrap", "original_name", "formatted_file_size", "file_type_label", "gridColumn", "disabled", "justifyContent", "minHeight", "pt", "mr"], "sources": ["C:/laragon/www/frontend/src/pages/dashboard/Order.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Stepper,\n  Step,\n  StepLabel,\n  Button,\n  Paper,\n  Card,\n  CardContent,\n  CardMedia,\n  Chip,\n  Alert,\n  TextField,\n\n  Divider,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n} from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport printingService, { PrintingCategory, PrintingProduct, OrderItem, PriceCalculation, PrintingOrder, OrderFile } from '../../services/printingService';\nimport FileUpload from '../../components/dashboard/FileUpload';\nimport ErrorBoundary from '../../components/common/ErrorBoundary';\n\nconst steps = ['Select Category', 'Choose Product', 'Configure Order', 'Upload Files', 'Review & Submit'];\n\n// Helper function to safely convert values to numbers for display\nconst safeNumber = (value: any): number => {\n  const num = Number(value);\n  return isNaN(num) ? 0 : num;\n};\n\nconst Order: React.FC = () => {\n  const navigate = useNavigate();\n  const [activeStep, setActiveStep] = useState(0);\n  const [categories, setCategories] = useState<PrintingCategory[]>([]);\n  const [products, setProducts] = useState<PrintingProduct[]>([]);\n  const [selectedCategory, setSelectedCategory] = useState<PrintingCategory | null>(null);\n  const [selectedProduct, setSelectedProduct] = useState<PrintingProduct | null>(null);\n  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Product configuration state\n  const [quantity, setQuantity] = useState<number | string>(1);\n  const [selectedOptions, setSelectedOptions] = useState<Record<string, any>>({});\n  const [priceCalculation, setPriceCalculation] = useState<PriceCalculation | null>(null);\n  const [calculatingPrice, setCalculatingPrice] = useState(false);\n\n  // Order and file upload state\n  const [createdOrder, setCreatedOrder] = useState<PrintingOrder | null>(null);\n  const [uploadedFiles, setUploadedFiles] = useState<OrderFile[]>([]);\n  const [submittingOrder, setSubmittingOrder] = useState(false);\n\n  useEffect(() => {\n    loadCategories();\n  }, []);\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      const data = await printingService.getCategories();\n      setCategories(data);\n    } catch (err) {\n      setError('Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadProducts = async (categorySlug: string) => {\n    try {\n      setLoading(true);\n      const data = await printingService.getProducts(categorySlug);\n      setProducts(data);\n    } catch (err) {\n      setError('Failed to load products');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCategorySelect = async (category: PrintingCategory) => {\n    setSelectedCategory(category);\n    await loadProducts(category.slug);\n    setActiveStep(1);\n  };\n\n  const handleProductSelect = (product: PrintingProduct) => {\n    setSelectedProduct(product);\n    // Reset configuration when selecting a new product\n    // Ensure we use the product's actual minimum quantity, with a fallback to 1\n    const initialQuantity = product.min_quantity && product.min_quantity > 0 ? product.min_quantity : 1;\n    setQuantity(initialQuantity);\n    setSelectedOptions({});\n    setPriceCalculation(null);\n    setActiveStep(2);\n  };\n\n  // Calculate price when quantity or options change\n  const calculatePrice = async () => {\n    if (!selectedProduct) return;\n\n    // Convert quantity to number for API call\n    const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n    const minQty = selectedProduct.min_quantity && selectedProduct.min_quantity > 0 ? selectedProduct.min_quantity : 1;\n    if (isNaN(numericQuantity) || numericQuantity < minQty) return;\n\n    setCalculatingPrice(true);\n    try {\n      const result = await printingService.calculatePrice(\n        selectedProduct.id,\n        numericQuantity,\n        selectedOptions\n      );\n      setPriceCalculation(result);\n    } catch (err) {\n      setError('Failed to calculate price');\n      console.error('Price calculation error:', err);\n    } finally {\n      setCalculatingPrice(false);\n    }\n  };\n\n  // Effect to calculate price when quantity or options change\n  useEffect(() => {\n    const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n    const minQty = selectedProduct?.min_quantity && selectedProduct.min_quantity > 0 ? selectedProduct.min_quantity : 1;\n    if (selectedProduct && !isNaN(numericQuantity) && numericQuantity >= minQty) {\n      const timeoutId = setTimeout(() => {\n        calculatePrice();\n      }, 300); // Debounce price calculation\n\n      return () => clearTimeout(timeoutId);\n    } else {\n      setPriceCalculation(null);\n    }\n  }, [selectedProduct, quantity, selectedOptions]);\n\n  const handleNext = async () => {\n    // If moving from configuration step, save the order item and create order\n    if (activeStep === 2 && selectedProduct && priceCalculation) {\n      const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n      const orderItem: OrderItem = {\n        product_id: selectedProduct.id,\n        quantity: numericQuantity,\n        selected_options: selectedOptions,\n        specifications: selectedProduct.specifications,\n      };\n      setOrderItems([orderItem]);\n\n      // Create the order when moving to file upload step\n      await createOrder([orderItem]);\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  const createOrder = async (items: OrderItem[]) => {\n    try {\n      setLoading(true);\n      const orderData = {\n        items,\n        special_instructions: '',\n        delivery_method: 'standard',\n      };\n\n      console.log('Creating order with data:', orderData);\n      const order = await printingService.createOrder(orderData);\n      setCreatedOrder(order);\n      console.log('Order created successfully:', order);\n    } catch (err: any) {\n      console.error('Order creation error details:', err);\n      console.error('Error response:', err.response?.data);\n\n      let errorMessage = 'Failed to create order';\n      if (err.response?.data?.message) {\n        errorMessage = err.response.data.message;\n      } else if (err.response?.data?.errors) {\n        const errors = Object.values(err.response.data.errors).flat();\n        errorMessage = errors.join(', ');\n      } else if (err.message) {\n        errorMessage = err.message;\n      }\n\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFilesUploaded = (files: OrderFile[]) => {\n    setUploadedFiles(prev => [...prev, ...files]);\n  };\n\n  const handleFileUploadError = (error: string) => {\n    setError(error);\n  };\n\n  const handleSubmitOrder = async () => {\n    if (!createdOrder) {\n      setError('No order to submit');\n      return;\n    }\n\n    try {\n      setSubmittingOrder(true);\n      // Order is already created, just navigate to success or orders page\n      navigate('/dashboard/orders');\n    } catch (err: any) {\n      setError(err.message || 'Failed to submit order');\n    } finally {\n      setSubmittingOrder(false);\n    }\n  };\n\n  const renderStepContent = (step: number) => {\n    switch (step) {\n      case 0:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Select a Printing Category\n            </Typography>\n            <Box\n              sx={{\n                display: 'grid',\n                gridTemplateColumns: {\n                  xs: '1fr',\n                  sm: 'repeat(2, 1fr)',\n                  md: 'repeat(3, 1fr)'\n                },\n                gap: 3\n              }}\n            >\n              {categories.map((category) => (\n                <Card\n                  key={category.id}\n                  sx={{ cursor: 'pointer', '&:hover': { elevation: 4 } }}\n                  onClick={() => handleCategorySelect(category)}\n                >\n                  {category.image && (\n                    <CardMedia\n                      component=\"img\"\n                      height=\"140\"\n                      image={category.image}\n                      alt={category.name}\n                    />\n                  )}\n                  <CardContent>\n                    <Typography gutterBottom variant=\"h6\" component=\"div\">\n                      {category.name}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {category.description}\n                    </Typography>\n                    {category.products_count !== undefined && (\n                      <Chip\n                        label={`${category.products_count} products`}\n                        size=\"small\"\n                        sx={{ mt: 1 }}\n                      />\n                    )}\n                  </CardContent>\n                </Card>\n              ))}\n            </Box>\n          </Box>\n        );\n\n      case 1:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Choose a Product from {selectedCategory?.name}\n            </Typography>\n            <Box\n              sx={{\n                display: 'grid',\n                gridTemplateColumns: {\n                  xs: '1fr',\n                  sm: 'repeat(2, 1fr)',\n                  md: 'repeat(3, 1fr)'\n                },\n                gap: 3\n              }}\n            >\n              {products.map((product) => (\n                <Card\n                  key={product.id}\n                  sx={{ cursor: 'pointer', '&:hover': { elevation: 4 } }}\n                  onClick={() => handleProductSelect(product)}\n                >\n                  {product.image && (\n                    <CardMedia\n                      component=\"img\"\n                      height=\"140\"\n                      image={product.image}\n                      alt={product.name}\n                    />\n                  )}\n                  <CardContent>\n                    <Typography gutterBottom variant=\"h6\" component=\"div\">\n                      {product.name}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                      {product.description}\n                    </Typography>\n                    <Typography variant=\"h6\" color=\"primary\">\n                      {product.formatted_base_price}\n                    </Typography>\n                    <Typography variant=\"caption\" display=\"block\">\n                      Min: {product.min_quantity} | Production: {product.production_time_days} days\n                    </Typography>\n                  </CardContent>\n                </Card>\n              ))}\n            </Box>\n          </Box>\n        );\n\n      case 2:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Configure Your Order\n            </Typography>\n\n            {selectedProduct && (\n              <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>\n                {/* Product Summary */}\n                <Box sx={{ flex: 1 }}>\n                  <Paper sx={{ p: 3 }}>\n                    <Typography variant=\"h6\" gutterBottom>\n                      {selectedProduct.name}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                      {selectedProduct.description}\n                    </Typography>\n\n                    {/* Specifications */}\n                    <Typography variant=\"subtitle2\" gutterBottom>\n                      Specifications:\n                    </Typography>\n                    <Box sx={{ mb: 2 }}>\n                      {Object.entries(selectedProduct.specifications || {}).map(([key, value]) => (\n                        <Typography key={key} variant=\"body2\" sx={{ mb: 0.5 }}>\n                          <strong>{key}:</strong> {value}\n                        </Typography>\n                      ))}\n                    </Box>\n\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Production Time: {selectedProduct.production_time_days} days\n                    </Typography>\n                  </Paper>\n                </Box>\n\n                {/* Configuration Form */}\n                <Box sx={{ flex: 1 }}>\n                  <Paper sx={{ p: 3 }}>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Order Configuration\n                    </Typography>\n\n                    {/* Quantity Input - Enhanced for Manual Text Entry */}\n                    <TextField\n                      label=\"Quantity\"\n                      type=\"text\"\n                      value={quantity}\n                      onChange={(e) => {\n                        const inputValue = e.target.value;\n\n                        // Allow empty input for better user experience while typing\n                        if (inputValue === '') {\n                          setQuantity('');\n                          return;\n                        }\n\n                        // Only allow numeric characters\n                        if (!/^\\d+$/.test(inputValue)) {\n                          return;\n                        }\n\n                        const numericValue = parseInt(inputValue, 10);\n                        const maxQty = selectedProduct.max_quantity;\n\n                        // Only enforce maximum constraint during typing, not minimum\n                        // This allows users to type freely above minimum quantity\n                        if (maxQty && numericValue > maxQty) {\n                          setQuantity(maxQty);\n                        } else {\n                          setQuantity(numericValue);\n                        }\n                      }}\n                      onBlur={(e) => {\n                        // Apply full validation when user leaves the field\n                        const currentValue = e.target.value;\n                        const minQty = selectedProduct.min_quantity && selectedProduct.min_quantity > 0 ? selectedProduct.min_quantity : 1;\n                        const maxQty = selectedProduct.max_quantity;\n\n                        if (currentValue === '' || isNaN(parseInt(currentValue, 10))) {\n                          // If empty or invalid, set to minimum quantity\n                          setQuantity(minQty);\n                        } else {\n                          // Apply min/max constraints\n                          const numericValue = parseInt(currentValue, 10);\n                          let validatedQuantity = Math.max(minQty, numericValue);\n                          if (maxQty && validatedQuantity > maxQty) {\n                            validatedQuantity = maxQty;\n                          }\n                          setQuantity(validatedQuantity);\n                        }\n                      }}\n                      onKeyDown={(e) => {\n                        // Handle Enter key to apply validation immediately\n                        if (e.key === 'Enter') {\n                          e.currentTarget.blur(); // Trigger onBlur validation\n                          return;\n                        }\n\n                        // Allow navigation keys, backspace, delete, tab, escape\n                        if ([\n                          'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',\n                          'Backspace', 'Delete', 'Tab', 'Escape',\n                          'Home', 'End'\n                        ].includes(e.key)) {\n                          return;\n                        }\n\n                        // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z\n                        if (e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) {\n                          return;\n                        }\n\n                        // Only allow numeric characters\n                        if (!/^\\d$/.test(e.key)) {\n                          e.preventDefault();\n                        }\n                      }}\n                      slotProps={{\n                        htmlInput: {\n                          inputMode: 'numeric',\n                          pattern: '[0-9]*',\n                          autoComplete: 'off',\n                          style: {\n                            fontSize: '16px', // Prevents zoom on mobile devices\n                            textAlign: 'left'\n                          }\n                        }\n                      }}\n                      helperText={`Min: ${selectedProduct.min_quantity && selectedProduct.min_quantity > 0 ? selectedProduct.min_quantity : 1}${selectedProduct.max_quantity ? `, Max: ${selectedProduct.max_quantity}` : ''} • Type quantity directly`}\n                      fullWidth\n                      sx={{\n                        mb: 3,\n                        '& .MuiOutlinedInput-root': {\n                          '&:hover fieldset': {\n                            borderColor: 'primary.main',\n                          },\n                          '&.Mui-focused fieldset': {\n                            borderColor: 'primary.main',\n                          }\n                        }\n                      }}\n                    />\n\n                    {/* Quantity-based Pricing Tiers */}\n                    {selectedProduct.options?.quantity_pricing && (\n                      <Box sx={{ mb: 3 }}>\n                        <Typography variant=\"subtitle1\" gutterBottom>\n                          Pricing Tiers:\n                        </Typography>\n                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n                          {selectedProduct.options.quantity_pricing.map((tier: any, index: number) => {\n                            const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n                            const isCurrentTier = !isNaN(numericQuantity) && numericQuantity >= tier.min_quantity &&\n                              (index === selectedProduct.options.quantity_pricing.length - 1 ||\n                               numericQuantity < selectedProduct.options.quantity_pricing[index + 1]?.min_quantity);\n\n                            return (\n                              <Box\n                                key={index}\n                                sx={{\n                                  p: 1.5,\n                                  borderRadius: 1,\n                                  border: '1px solid',\n                                  borderColor: isCurrentTier ? 'primary.main' : 'grey.300',\n                                  backgroundColor: isCurrentTier ? 'primary.50' : 'transparent',\n                                  transition: 'all 0.2s ease-in-out'\n                                }}\n                              >\n                                <Typography\n                                  variant=\"body2\"\n                                  sx={{\n                                    fontWeight: isCurrentTier ? 600 : 400,\n                                    color: isCurrentTier ? 'primary.main' : 'text.primary'\n                                  }}\n                                >\n                                  {tier.min_quantity}+ units: RM {safeNumber(tier.price_per_unit).toFixed(2)} each\n                                  {isCurrentTier && (\n                                    <Chip\n                                      label=\"Current\"\n                                      size=\"small\"\n                                      color=\"primary\"\n                                      sx={{ ml: 1, height: 20 }}\n                                    />\n                                  )}\n                                </Typography>\n                              </Box>\n                            );\n                          })}\n                        </Box>\n                      </Box>\n                    )}\n\n                    <Divider sx={{ my: 2 }} />\n\n                    {/* Price Calculation */}\n                    <Box>\n                      <Typography variant=\"h6\" gutterBottom>\n                        Price Summary\n                      </Typography>\n\n\n                      {calculatingPrice ? (\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                          <Typography variant=\"body2\">Calculating...</Typography>\n                        </Box>\n                      ) : priceCalculation ? (\n                        <Box>\n                          <Typography variant=\"body1\">\n                            Unit Price: RM {safeNumber(priceCalculation.unit_price).toFixed(2)}\n                          </Typography>\n                          <Typography variant=\"body1\">\n                            Quantity: {typeof quantity === 'string' ? parseInt(quantity, 10) || 0 : quantity}\n                          </Typography>\n                          <Typography variant=\"h5\" color=\"primary\" sx={{ mt: 1 }}>\n                            Total: {priceCalculation.formatted_total_price}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1, display: 'block' }}>\n                            Estimated production time: {priceCalculation.production_time_days} days\n                          </Typography>\n                        </Box>\n                      ) : (() => {\n                          const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n                          const minQty = selectedProduct?.min_quantity && selectedProduct.min_quantity > 0 ? selectedProduct.min_quantity : 1;\n                          return !isNaN(numericQuantity) && numericQuantity >= minQty;\n                        })() ? (\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Loading pricing...\n                        </Typography>\n                      ) : (\n                        <Typography variant=\"body2\" color=\"error\">\n                          Please enter a valid quantity (minimum: {selectedProduct?.min_quantity && selectedProduct.min_quantity > 0 ? selectedProduct.min_quantity : 1})\n                        </Typography>\n                      )}\n                    </Box>\n                  </Paper>\n                </Box>\n              </Box>\n            )}\n          </Box>\n        );\n\n      case 3:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Upload Artwork Files\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n              Upload your artwork files for printing. You can upload multiple files and specify the file type for each upload.\n            </Typography>\n\n            {createdOrder ? (\n              <ErrorBoundary\n                fallback={\n                  <Alert severity=\"error\" sx={{ mb: 2 }}>\n                    Failed to load file upload component. Please refresh the page and try again.\n                  </Alert>\n                }\n              >\n                <FileUpload\n                  orderId={createdOrder.id}\n                  onFilesUploaded={handleFilesUploaded}\n                  onError={handleFileUploadError}\n                />\n              </ErrorBoundary>\n            ) : (\n              <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                Creating order... Please wait.\n              </Alert>\n            )}\n          </Box>\n        );\n\n      case 4:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Review Your Order\n            </Typography>\n\n            {createdOrder && (\n              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3 }}>\n                {/* Order Summary */}\n                <Paper sx={{ p: 3 }}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Order Summary\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                    Order #{createdOrder.order_number}\n                  </Typography>\n\n                  {selectedProduct && (\n                    <Box sx={{ mt: 2 }}>\n                      <Typography variant=\"subtitle2\">Product:</Typography>\n                      <Typography variant=\"body2\">{selectedProduct.name}</Typography>\n\n                      <Typography variant=\"subtitle2\" sx={{ mt: 1 }}>Quantity:</Typography>\n                      <Typography variant=\"body2\">{typeof quantity === 'string' ? parseInt(quantity, 10) || 0 : quantity}</Typography>\n\n                      {priceCalculation && (\n                        <>\n                          <Typography variant=\"subtitle2\" sx={{ mt: 1 }}>Total Price:</Typography>\n                          <Typography variant=\"h6\" color=\"primary\">\n                            {priceCalculation.formatted_total_price}\n                          </Typography>\n                        </>\n                      )}\n\n                      <Typography variant=\"subtitle2\" sx={{ mt: 1 }}>Status:</Typography>\n                      <Chip\n                        label={createdOrder.status || 'Pending'}\n                        color=\"warning\"\n                        size=\"small\"\n                      />\n                    </Box>\n                  )}\n                </Paper>\n\n                {/* Files Summary */}\n                <Paper sx={{ p: 3 }}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Uploaded Files ({uploadedFiles.length})\n                  </Typography>\n\n                  {uploadedFiles.length > 0 ? (\n                    <Box>\n                      {uploadedFiles.map((file, index) => (\n                        <Box key={file.id} sx={{ mb: 1, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>\n                          <Typography variant=\"body2\" noWrap>\n                            {file.original_name}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {file.formatted_file_size} • {file.file_type_label}\n                          </Typography>\n                        </Box>\n                      ))}\n                    </Box>\n                  ) : (\n                    <Alert severity=\"warning\">\n                      No files uploaded yet. You can still submit the order and upload files later.\n                    </Alert>\n                  )}\n                </Paper>\n\n                {/* Submit Button */}\n                <Box sx={{ gridColumn: '1 / -1', textAlign: 'center', mt: 2 }}>\n                  <Button\n                    variant=\"contained\"\n                    size=\"large\"\n                    onClick={handleSubmitOrder}\n                    disabled={submittingOrder}\n                  >\n                    {submittingOrder ? 'Submitting...' : 'Submit Order'}\n                  </Button>\n                </Box>\n              </Box>\n            )}\n          </Box>\n        );\n\n      default:\n        return 'Unknown step';\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <Typography>Loading...</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        Create New Order\n      </Typography>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      <Paper sx={{ p: 3 }}>\n        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>\n          {steps.map((label) => (\n            <Step key={label}>\n              <StepLabel>{label}</StepLabel>\n            </Step>\n          ))}\n        </Stepper>\n\n        <Box sx={{ minHeight: 400 }}>\n          {renderStepContent(activeStep)}\n        </Box>\n\n        <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>\n          <Button\n            color=\"inherit\"\n            disabled={activeStep === 0}\n            onClick={handleBack}\n            sx={{ mr: 1 }}\n          >\n            Back\n          </Button>\n          <Box sx={{ flex: '1 1 auto' }} />\n          {activeStep < steps.length - 1 && (\n            <Button\n              onClick={handleNext}\n              disabled={\n                loading ||\n                (activeStep === 0 && !selectedCategory) ||\n                (activeStep === 1 && !selectedProduct) ||\n                (activeStep === 2 && (() => {\n                  const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n                  const minQty = selectedProduct?.min_quantity && selectedProduct.min_quantity > 0 ? selectedProduct.min_quantity : 1;\n                  return !priceCalculation || isNaN(numericQuantity) || numericQuantity < minQty;\n                })()) ||\n                (activeStep === 3 && !createdOrder)\n              }\n            >\n              {loading ? 'Creating Order...' : 'Next'}\n            </Button>\n          )}\n          {activeStep === steps.length - 1 && (\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSubmitOrder}\n              disabled={submittingOrder || !createdOrder}\n            >\n              {submittingOrder ? 'Submitting...' : 'Complete Order'}\n            </Button>\n          )}\n        </Box>\n      </Paper>\n    </Box>\n  );\n};\n\nexport default Order;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,UAAU,CACVC,OAAO,CACPC,IAAI,CACJC,SAAS,CACTC,MAAM,CACNC,KAAK,CACLC,IAAI,CACJC,WAAW,CACXC,SAAS,CACTC,IAAI,CACJC,KAAK,CACLC,SAAS,CAETC,OAAO,KAKF,eAAe,CACtB,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,eAAe,KAAoG,gCAAgC,CAC1J,MAAO,CAAAC,UAAU,KAAM,uCAAuC,CAC9D,MAAO,CAAAC,aAAa,KAAM,uCAAuC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAElE,KAAM,CAAAC,KAAK,CAAG,CAAC,iBAAiB,CAAE,gBAAgB,CAAE,iBAAiB,CAAE,cAAc,CAAE,iBAAiB,CAAC,CAEzG;AACA,KAAM,CAAAC,UAAU,CAAIC,KAAU,EAAa,CACzC,KAAM,CAAAC,GAAG,CAAGC,MAAM,CAACF,KAAK,CAAC,CACzB,MAAO,CAAAG,KAAK,CAACF,GAAG,CAAC,CAAG,CAAC,CAAGA,GAAG,CAC7B,CAAC,CAED,KAAM,CAAAG,KAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACkB,UAAU,CAAEC,aAAa,CAAC,CAAGnC,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAACoC,UAAU,CAAEC,aAAa,CAAC,CAAGrC,QAAQ,CAAqB,EAAE,CAAC,CACpE,KAAM,CAACsC,QAAQ,CAAEC,WAAW,CAAC,CAAGvC,QAAQ,CAAoB,EAAE,CAAC,CAC/D,KAAM,CAACwC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGzC,QAAQ,CAA0B,IAAI,CAAC,CACvF,KAAM,CAAC0C,eAAe,CAAEC,kBAAkB,CAAC,CAAG3C,QAAQ,CAAyB,IAAI,CAAC,CACpF,KAAM,CAAC4C,UAAU,CAAEC,aAAa,CAAC,CAAG7C,QAAQ,CAAc,EAAE,CAAC,CAC7D,KAAM,CAAC8C,OAAO,CAAEC,UAAU,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACgD,KAAK,CAAEC,QAAQ,CAAC,CAAGjD,QAAQ,CAAgB,IAAI,CAAC,CAEvD;AACA,KAAM,CAACkD,QAAQ,CAAEC,WAAW,CAAC,CAAGnD,QAAQ,CAAkB,CAAC,CAAC,CAC5D,KAAM,CAACoD,eAAe,CAAEC,kBAAkB,CAAC,CAAGrD,QAAQ,CAAsB,CAAC,CAAC,CAAC,CAC/E,KAAM,CAACsD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvD,QAAQ,CAA0B,IAAI,CAAC,CACvF,KAAM,CAACwD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGzD,QAAQ,CAAC,KAAK,CAAC,CAE/D;AACA,KAAM,CAAC0D,YAAY,CAAEC,eAAe,CAAC,CAAG3D,QAAQ,CAAuB,IAAI,CAAC,CAC5E,KAAM,CAAC4D,aAAa,CAAEC,gBAAgB,CAAC,CAAG7D,QAAQ,CAAc,EAAE,CAAC,CACnE,KAAM,CAAC8D,eAAe,CAAEC,kBAAkB,CAAC,CAAG/D,QAAQ,CAAC,KAAK,CAAC,CAE7DC,SAAS,CAAC,IAAM,CACd+D,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACFjB,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAkB,IAAI,CAAG,KAAM,CAAAhD,eAAe,CAACiD,aAAa,CAAC,CAAC,CAClD7B,aAAa,CAAC4B,IAAI,CAAC,CACrB,CAAE,MAAOE,GAAG,CAAE,CACZlB,QAAQ,CAAC,2BAA2B,CAAC,CACvC,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAqB,YAAY,CAAG,KAAO,CAAAC,YAAoB,EAAK,CACnD,GAAI,CACFtB,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAkB,IAAI,CAAG,KAAM,CAAAhD,eAAe,CAACqD,WAAW,CAACD,YAAY,CAAC,CAC5D9B,WAAW,CAAC0B,IAAI,CAAC,CACnB,CAAE,MAAOE,GAAG,CAAE,CACZlB,QAAQ,CAAC,yBAAyB,CAAC,CACrC,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAwB,oBAAoB,CAAG,KAAO,CAAAC,QAA0B,EAAK,CACjE/B,mBAAmB,CAAC+B,QAAQ,CAAC,CAC7B,KAAM,CAAAJ,YAAY,CAACI,QAAQ,CAACC,IAAI,CAAC,CACjCtC,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,CAED,KAAM,CAAAuC,mBAAmB,CAAIC,OAAwB,EAAK,CACxDhC,kBAAkB,CAACgC,OAAO,CAAC,CAC3B;AACA;AACA,KAAM,CAAAC,eAAe,CAAGD,OAAO,CAACE,YAAY,EAAIF,OAAO,CAACE,YAAY,CAAG,CAAC,CAAGF,OAAO,CAACE,YAAY,CAAG,CAAC,CACnG1B,WAAW,CAACyB,eAAe,CAAC,CAC5BvB,kBAAkB,CAAC,CAAC,CAAC,CAAC,CACtBE,mBAAmB,CAAC,IAAI,CAAC,CACzBpB,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,CAED;AACA,KAAM,CAAA2C,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CAACpC,eAAe,CAAE,OAEtB;AACA,KAAM,CAAAqC,eAAe,CAAG,MAAO,CAAA7B,QAAQ,GAAK,QAAQ,CAAG8B,QAAQ,CAAC9B,QAAQ,CAAE,EAAE,CAAC,CAAGA,QAAQ,CACxF,KAAM,CAAA+B,MAAM,CAAGvC,eAAe,CAACmC,YAAY,EAAInC,eAAe,CAACmC,YAAY,CAAG,CAAC,CAAGnC,eAAe,CAACmC,YAAY,CAAG,CAAC,CAClH,GAAI9C,KAAK,CAACgD,eAAe,CAAC,EAAIA,eAAe,CAAGE,MAAM,CAAE,OAExDxB,mBAAmB,CAAC,IAAI,CAAC,CACzB,GAAI,CACF,KAAM,CAAAyB,MAAM,CAAG,KAAM,CAAAjE,eAAe,CAAC6D,cAAc,CACjDpC,eAAe,CAACyC,EAAE,CAClBJ,eAAe,CACf3B,eACF,CAAC,CACDG,mBAAmB,CAAC2B,MAAM,CAAC,CAC7B,CAAE,MAAOf,GAAG,CAAE,CACZlB,QAAQ,CAAC,2BAA2B,CAAC,CACrCmC,OAAO,CAACpC,KAAK,CAAC,0BAA0B,CAAEmB,GAAG,CAAC,CAChD,CAAC,OAAS,CACRV,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CACF,CAAC,CAED;AACAxD,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8E,eAAe,CAAG,MAAO,CAAA7B,QAAQ,GAAK,QAAQ,CAAG8B,QAAQ,CAAC9B,QAAQ,CAAE,EAAE,CAAC,CAAGA,QAAQ,CACxF,KAAM,CAAA+B,MAAM,CAAGvC,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEmC,YAAY,EAAInC,eAAe,CAACmC,YAAY,CAAG,CAAC,CAAGnC,eAAe,CAACmC,YAAY,CAAG,CAAC,CACnH,GAAInC,eAAe,EAAI,CAACX,KAAK,CAACgD,eAAe,CAAC,EAAIA,eAAe,EAAIE,MAAM,CAAE,CAC3E,KAAM,CAAAI,SAAS,CAAGC,UAAU,CAAC,IAAM,CACjCR,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,GAAG,CAAC,CAAE;AAET,MAAO,IAAMS,YAAY,CAACF,SAAS,CAAC,CACtC,CAAC,IAAM,CACL9B,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CACF,CAAC,CAAE,CAACb,eAAe,CAAEQ,QAAQ,CAAEE,eAAe,CAAC,CAAC,CAEhD,KAAM,CAAAoC,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B;AACA,GAAItD,UAAU,GAAK,CAAC,EAAIQ,eAAe,EAAIY,gBAAgB,CAAE,CAC3D,KAAM,CAAAyB,eAAe,CAAG,MAAO,CAAA7B,QAAQ,GAAK,QAAQ,CAAG8B,QAAQ,CAAC9B,QAAQ,CAAE,EAAE,CAAC,CAAGA,QAAQ,CACxF,KAAM,CAAAuC,SAAoB,CAAG,CAC3BC,UAAU,CAAEhD,eAAe,CAACyC,EAAE,CAC9BjC,QAAQ,CAAE6B,eAAe,CACzBY,gBAAgB,CAAEvC,eAAe,CACjCwC,cAAc,CAAElD,eAAe,CAACkD,cAClC,CAAC,CACD/C,aAAa,CAAC,CAAC4C,SAAS,CAAC,CAAC,CAE1B;AACA,KAAM,CAAAI,WAAW,CAAC,CAACJ,SAAS,CAAC,CAAC,CAChC,CAEAtD,aAAa,CAAE2D,cAAc,EAAKA,cAAc,CAAG,CAAC,CAAC,CACvD,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB5D,aAAa,CAAE2D,cAAc,EAAKA,cAAc,CAAG,CAAC,CAAC,CACvD,CAAC,CAED,KAAM,CAAAD,WAAW,CAAG,KAAO,CAAAG,KAAkB,EAAK,CAChD,GAAI,CACFjD,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAkD,SAAS,CAAG,CAChBD,KAAK,CACLE,oBAAoB,CAAE,EAAE,CACxBC,eAAe,CAAE,UACnB,CAAC,CAEDf,OAAO,CAACgB,GAAG,CAAC,2BAA2B,CAAEH,SAAS,CAAC,CACnD,KAAM,CAAAI,KAAK,CAAG,KAAM,CAAApF,eAAe,CAAC4E,WAAW,CAACI,SAAS,CAAC,CAC1DtC,eAAe,CAAC0C,KAAK,CAAC,CACtBjB,OAAO,CAACgB,GAAG,CAAC,6BAA6B,CAAEC,KAAK,CAAC,CACnD,CAAE,MAAOlC,GAAQ,CAAE,KAAAmC,aAAA,CAAAC,cAAA,CAAAC,mBAAA,CAAAC,cAAA,CAAAC,mBAAA,CACjBtB,OAAO,CAACpC,KAAK,CAAC,+BAA+B,CAAEmB,GAAG,CAAC,CACnDiB,OAAO,CAACpC,KAAK,CAAC,iBAAiB,EAAAsD,aAAA,CAAEnC,GAAG,CAACwC,QAAQ,UAAAL,aAAA,iBAAZA,aAAA,CAAcrC,IAAI,CAAC,CAEpD,GAAI,CAAA2C,YAAY,CAAG,wBAAwB,CAC3C,IAAAL,cAAA,CAAIpC,GAAG,CAACwC,QAAQ,UAAAJ,cAAA,YAAAC,mBAAA,CAAZD,cAAA,CAActC,IAAI,UAAAuC,mBAAA,WAAlBA,mBAAA,CAAoBK,OAAO,CAAE,CAC/BD,YAAY,CAAGzC,GAAG,CAACwC,QAAQ,CAAC1C,IAAI,CAAC4C,OAAO,CAC1C,CAAC,IAAM,KAAAJ,cAAA,CAAItC,GAAG,CAACwC,QAAQ,UAAAF,cAAA,YAAAC,mBAAA,CAAZD,cAAA,CAAcxC,IAAI,UAAAyC,mBAAA,WAAlBA,mBAAA,CAAoBI,MAAM,CAAE,CACrC,KAAM,CAAAA,MAAM,CAAGC,MAAM,CAACC,MAAM,CAAC7C,GAAG,CAACwC,QAAQ,CAAC1C,IAAI,CAAC6C,MAAM,CAAC,CAACG,IAAI,CAAC,CAAC,CAC7DL,YAAY,CAAGE,MAAM,CAACI,IAAI,CAAC,IAAI,CAAC,CAClC,CAAC,IAAM,IAAI/C,GAAG,CAAC0C,OAAO,CAAE,CACtBD,YAAY,CAAGzC,GAAG,CAAC0C,OAAO,CAC5B,CAEA5D,QAAQ,CAAC2D,YAAY,CAAC,CACxB,CAAC,OAAS,CACR7D,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAoE,mBAAmB,CAAIC,KAAkB,EAAK,CAClDvD,gBAAgB,CAACwD,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,GAAGD,KAAK,CAAC,CAAC,CAC/C,CAAC,CAED,KAAM,CAAAE,qBAAqB,CAAItE,KAAa,EAAK,CAC/CC,QAAQ,CAACD,KAAK,CAAC,CACjB,CAAC,CAED,KAAM,CAAAuE,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CAAC7D,YAAY,CAAE,CACjBT,QAAQ,CAAC,oBAAoB,CAAC,CAC9B,OACF,CAEA,GAAI,CACFc,kBAAkB,CAAC,IAAI,CAAC,CACxB;AACA9B,QAAQ,CAAC,mBAAmB,CAAC,CAC/B,CAAE,MAAOkC,GAAQ,CAAE,CACjBlB,QAAQ,CAACkB,GAAG,CAAC0C,OAAO,EAAI,wBAAwB,CAAC,CACnD,CAAC,OAAS,CACR9C,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CACF,CAAC,CAED,KAAM,CAAAyD,iBAAiB,CAAIC,IAAY,EAAK,KAAAC,qBAAA,CAC1C,OAAQD,IAAI,EACV,IAAK,EAAC,CACJ,mBACElG,KAAA,CAACrB,GAAG,EAAAyH,QAAA,eACFtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,4BAEtC,CAAY,CAAC,cACbtG,IAAA,CAACnB,GAAG,EACF4H,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfC,mBAAmB,CAAE,CACnBC,EAAE,CAAE,KAAK,CACTC,EAAE,CAAE,gBAAgB,CACpBC,EAAE,CAAE,gBACN,CAAC,CACDC,GAAG,CAAE,CACP,CAAE,CAAAT,QAAA,CAEDvF,UAAU,CAACiG,GAAG,CAAE7D,QAAQ,eACvBjD,KAAA,CAACd,IAAI,EAEHqH,EAAE,CAAE,CAAEQ,MAAM,CAAE,SAAS,CAAE,SAAS,CAAE,CAAEC,SAAS,CAAE,CAAE,CAAE,CAAE,CACvDC,OAAO,CAAEA,CAAA,GAAMjE,oBAAoB,CAACC,QAAQ,CAAE,CAAAmD,QAAA,EAE7CnD,QAAQ,CAACiE,KAAK,eACbpH,IAAA,CAACV,SAAS,EACR+H,SAAS,CAAC,KAAK,CACfC,MAAM,CAAC,KAAK,CACZF,KAAK,CAAEjE,QAAQ,CAACiE,KAAM,CACtBG,GAAG,CAAEpE,QAAQ,CAACqE,IAAK,CACpB,CACF,cACDtH,KAAA,CAACb,WAAW,EAAAiH,QAAA,eACVtG,IAAA,CAAClB,UAAU,EAAC0H,YAAY,MAACD,OAAO,CAAC,IAAI,CAACc,SAAS,CAAC,KAAK,CAAAf,QAAA,CAClDnD,QAAQ,CAACqE,IAAI,CACJ,CAAC,cACbxH,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC/CnD,QAAQ,CAACuE,WAAW,CACX,CAAC,CACZvE,QAAQ,CAACwE,cAAc,GAAKC,SAAS,eACpC5H,IAAA,CAACT,IAAI,EACHsI,KAAK,CAAE,GAAG1E,QAAQ,CAACwE,cAAc,WAAY,CAC7CG,IAAI,CAAC,OAAO,CACZrB,EAAE,CAAE,CAAEsB,EAAE,CAAE,CAAE,CAAE,CACf,CACF,EACU,CAAC,GA1BT5E,QAAQ,CAACW,EA2BV,CACP,CAAC,CACC,CAAC,EACH,CAAC,CAGV,IAAK,EAAC,CACJ,mBACE5D,KAAA,CAACrB,GAAG,EAAAyH,QAAA,eACFpG,KAAA,CAACpB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,EAAC,wBACd,CAACnF,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEqG,IAAI,EACnC,CAAC,cACbxH,IAAA,CAACnB,GAAG,EACF4H,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfC,mBAAmB,CAAE,CACnBC,EAAE,CAAE,KAAK,CACTC,EAAE,CAAE,gBAAgB,CACpBC,EAAE,CAAE,gBACN,CAAC,CACDC,GAAG,CAAE,CACP,CAAE,CAAAT,QAAA,CAEDrF,QAAQ,CAAC+F,GAAG,CAAE1D,OAAO,eACpBpD,KAAA,CAACd,IAAI,EAEHqH,EAAE,CAAE,CAAEQ,MAAM,CAAE,SAAS,CAAE,SAAS,CAAE,CAAEC,SAAS,CAAE,CAAE,CAAE,CAAE,CACvDC,OAAO,CAAEA,CAAA,GAAM9D,mBAAmB,CAACC,OAAO,CAAE,CAAAgD,QAAA,EAE3ChD,OAAO,CAAC8D,KAAK,eACZpH,IAAA,CAACV,SAAS,EACR+H,SAAS,CAAC,KAAK,CACfC,MAAM,CAAC,KAAK,CACZF,KAAK,CAAE9D,OAAO,CAAC8D,KAAM,CACrBG,GAAG,CAAEjE,OAAO,CAACkE,IAAK,CACnB,CACF,cACDtH,KAAA,CAACb,WAAW,EAAAiH,QAAA,eACVtG,IAAA,CAAClB,UAAU,EAAC0H,YAAY,MAACD,OAAO,CAAC,IAAI,CAACc,SAAS,CAAC,KAAK,CAAAf,QAAA,CAClDhD,OAAO,CAACkE,IAAI,CACH,CAAC,cACbxH,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,gBAAgB,CAAChB,EAAE,CAAE,CAAEuB,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,CAC9DhD,OAAO,CAACoE,WAAW,CACV,CAAC,cACb1H,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAACkB,KAAK,CAAC,SAAS,CAAAnB,QAAA,CACrChD,OAAO,CAAC2E,oBAAoB,CACnB,CAAC,cACb/H,KAAA,CAACpB,UAAU,EAACyH,OAAO,CAAC,SAAS,CAACG,OAAO,CAAC,OAAO,CAAAJ,QAAA,EAAC,OACvC,CAAChD,OAAO,CAACE,YAAY,CAAC,iBAAe,CAACF,OAAO,CAAC4E,oBAAoB,CAAC,OAC1E,EAAY,CAAC,EACF,CAAC,GAzBT5E,OAAO,CAACQ,EA0BT,CACP,CAAC,CACC,CAAC,EACH,CAAC,CAGV,IAAK,EAAC,CACJ,mBACE5D,KAAA,CAACrB,GAAG,EAAAyH,QAAA,eACFtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,sBAEtC,CAAY,CAAC,CAEZjF,eAAe,eACdnB,KAAA,CAACrB,GAAG,EAAC4H,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEyB,aAAa,CAAE,CAAEvB,EAAE,CAAE,QAAQ,CAAEE,EAAE,CAAE,KAAM,CAAC,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAT,QAAA,eAE/EtG,IAAA,CAACnB,GAAG,EAAC4H,EAAE,CAAE,CAAE2B,IAAI,CAAE,CAAE,CAAE,CAAA9B,QAAA,cACnBpG,KAAA,CAACf,KAAK,EAACsH,EAAE,CAAE,CAAE4B,CAAC,CAAE,CAAE,CAAE,CAAA/B,QAAA,eAClBtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAClCjF,eAAe,CAACmG,IAAI,CACX,CAAC,cACbxH,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,gBAAgB,CAAChB,EAAE,CAAE,CAAEuB,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,CAC9DjF,eAAe,CAACqG,WAAW,CAClB,CAAC,cAGb1H,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAF,QAAA,CAAC,iBAE7C,CAAY,CAAC,cACbtG,IAAA,CAACnB,GAAG,EAAC4H,EAAE,CAAE,CAAEuB,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,CAChBZ,MAAM,CAAC4C,OAAO,CAACjH,eAAe,CAACkD,cAAc,EAAI,CAAC,CAAC,CAAC,CAACyC,GAAG,CAACuB,IAAA,MAAC,CAACC,GAAG,CAAEjI,KAAK,CAAC,CAAAgI,IAAA,oBACrErI,KAAA,CAACpB,UAAU,EAAWyH,OAAO,CAAC,OAAO,CAACE,EAAE,CAAE,CAAEuB,EAAE,CAAE,GAAI,CAAE,CAAA1B,QAAA,eACpDpG,KAAA,WAAAoG,QAAA,EAASkC,GAAG,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACjI,KAAK,GADfiI,GAEL,CAAC,EACd,CAAC,CACC,CAAC,cAENtI,KAAA,CAACpB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,EAAC,mBAChC,CAACjF,eAAe,CAAC6G,oBAAoB,CAAC,OACzD,EAAY,CAAC,EACR,CAAC,CACL,CAAC,cAGNlI,IAAA,CAACnB,GAAG,EAAC4H,EAAE,CAAE,CAAE2B,IAAI,CAAE,CAAE,CAAE,CAAA9B,QAAA,cACnBpG,KAAA,CAACf,KAAK,EAACsH,EAAE,CAAE,CAAE4B,CAAC,CAAE,CAAE,CAAE,CAAA/B,QAAA,eAClBtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,qBAEtC,CAAY,CAAC,cAGbtG,IAAA,CAACP,SAAS,EACRoI,KAAK,CAAC,UAAU,CAChBY,IAAI,CAAC,MAAM,CACXlI,KAAK,CAAEsB,QAAS,CAChB6G,QAAQ,CAAGC,CAAC,EAAK,CACf,KAAM,CAAAC,UAAU,CAAGD,CAAC,CAACE,MAAM,CAACtI,KAAK,CAEjC;AACA,GAAIqI,UAAU,GAAK,EAAE,CAAE,CACrB9G,WAAW,CAAC,EAAE,CAAC,CACf,OACF,CAEA;AACA,GAAI,CAAC,OAAO,CAACgH,IAAI,CAACF,UAAU,CAAC,CAAE,CAC7B,OACF,CAEA,KAAM,CAAAG,YAAY,CAAGpF,QAAQ,CAACiF,UAAU,CAAE,EAAE,CAAC,CAC7C,KAAM,CAAAI,MAAM,CAAG3H,eAAe,CAAC4H,YAAY,CAE3C;AACA;AACA,GAAID,MAAM,EAAID,YAAY,CAAGC,MAAM,CAAE,CACnClH,WAAW,CAACkH,MAAM,CAAC,CACrB,CAAC,IAAM,CACLlH,WAAW,CAACiH,YAAY,CAAC,CAC3B,CACF,CAAE,CACFG,MAAM,CAAGP,CAAC,EAAK,CACb;AACA,KAAM,CAAAQ,YAAY,CAAGR,CAAC,CAACE,MAAM,CAACtI,KAAK,CACnC,KAAM,CAAAqD,MAAM,CAAGvC,eAAe,CAACmC,YAAY,EAAInC,eAAe,CAACmC,YAAY,CAAG,CAAC,CAAGnC,eAAe,CAACmC,YAAY,CAAG,CAAC,CAClH,KAAM,CAAAwF,MAAM,CAAG3H,eAAe,CAAC4H,YAAY,CAE3C,GAAIE,YAAY,GAAK,EAAE,EAAIzI,KAAK,CAACiD,QAAQ,CAACwF,YAAY,CAAE,EAAE,CAAC,CAAC,CAAE,CAC5D;AACArH,WAAW,CAAC8B,MAAM,CAAC,CACrB,CAAC,IAAM,CACL;AACA,KAAM,CAAAmF,YAAY,CAAGpF,QAAQ,CAACwF,YAAY,CAAE,EAAE,CAAC,CAC/C,GAAI,CAAAC,iBAAiB,CAAGC,IAAI,CAACC,GAAG,CAAC1F,MAAM,CAAEmF,YAAY,CAAC,CACtD,GAAIC,MAAM,EAAII,iBAAiB,CAAGJ,MAAM,CAAE,CACxCI,iBAAiB,CAAGJ,MAAM,CAC5B,CACAlH,WAAW,CAACsH,iBAAiB,CAAC,CAChC,CACF,CAAE,CACFG,SAAS,CAAGZ,CAAC,EAAK,CAChB;AACA,GAAIA,CAAC,CAACH,GAAG,GAAK,OAAO,CAAE,CACrBG,CAAC,CAACa,aAAa,CAACC,IAAI,CAAC,CAAC,CAAE;AACxB,OACF,CAEA;AACA,GAAI,CACF,WAAW,CAAE,YAAY,CAAE,SAAS,CAAE,WAAW,CACjD,WAAW,CAAE,QAAQ,CAAE,KAAK,CAAE,QAAQ,CACtC,MAAM,CAAE,KAAK,CACd,CAACC,QAAQ,CAACf,CAAC,CAACH,GAAG,CAAC,CAAE,CACjB,OACF,CAEA;AACA,GAAIG,CAAC,CAACgB,OAAO,EAAI,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAACD,QAAQ,CAACf,CAAC,CAACH,GAAG,CAACoB,WAAW,CAAC,CAAC,CAAC,CAAE,CACxE,OACF,CAEA;AACA,GAAI,CAAC,MAAM,CAACd,IAAI,CAACH,CAAC,CAACH,GAAG,CAAC,CAAE,CACvBG,CAAC,CAACkB,cAAc,CAAC,CAAC,CACpB,CACF,CAAE,CACFC,SAAS,CAAE,CACTC,SAAS,CAAE,CACTC,SAAS,CAAE,SAAS,CACpBC,OAAO,CAAE,QAAQ,CACjBC,YAAY,CAAE,KAAK,CACnBC,KAAK,CAAE,CACLC,QAAQ,CAAE,MAAM,CAAE;AAClBC,SAAS,CAAE,MACb,CACF,CACF,CAAE,CACFC,UAAU,CAAE,QAAQjJ,eAAe,CAACmC,YAAY,EAAInC,eAAe,CAACmC,YAAY,CAAG,CAAC,CAAGnC,eAAe,CAACmC,YAAY,CAAG,CAAC,GAAGnC,eAAe,CAAC4H,YAAY,CAAG,UAAU5H,eAAe,CAAC4H,YAAY,EAAE,CAAG,EAAE,2BAA4B,CAClOsB,SAAS,MACT9D,EAAE,CAAE,CACFuB,EAAE,CAAE,CAAC,CACL,0BAA0B,CAAE,CAC1B,kBAAkB,CAAE,CAClBwC,WAAW,CAAE,cACf,CAAC,CACD,wBAAwB,CAAE,CACxBA,WAAW,CAAE,cACf,CACF,CACF,CAAE,CACH,CAAC,CAGD,EAAAnE,qBAAA,CAAAhF,eAAe,CAACoJ,OAAO,UAAApE,qBAAA,iBAAvBA,qBAAA,CAAyBqE,gBAAgB,gBACxCxK,KAAA,CAACrB,GAAG,EAAC4H,EAAE,CAAE,CAAEuB,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,eACjBtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAF,QAAA,CAAC,gBAE7C,CAAY,CAAC,cACbtG,IAAA,CAACnB,GAAG,EAAC4H,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEyB,aAAa,CAAE,QAAQ,CAAEpB,GAAG,CAAE,CAAE,CAAE,CAAAT,QAAA,CAC3DjF,eAAe,CAACoJ,OAAO,CAACC,gBAAgB,CAAC1D,GAAG,CAAC,CAAC2D,IAAS,CAAEC,KAAa,GAAK,KAAAC,sBAAA,CAC1E,KAAM,CAAAnH,eAAe,CAAG,MAAO,CAAA7B,QAAQ,GAAK,QAAQ,CAAG8B,QAAQ,CAAC9B,QAAQ,CAAE,EAAE,CAAC,CAAGA,QAAQ,CACxF,KAAM,CAAAiJ,aAAa,CAAG,CAACpK,KAAK,CAACgD,eAAe,CAAC,EAAIA,eAAe,EAAIiH,IAAI,CAACnH,YAAY,GAClFoH,KAAK,GAAKvJ,eAAe,CAACoJ,OAAO,CAACC,gBAAgB,CAACK,MAAM,CAAG,CAAC,EAC7DrH,eAAe,GAAAmH,sBAAA,CAAGxJ,eAAe,CAACoJ,OAAO,CAACC,gBAAgB,CAACE,KAAK,CAAG,CAAC,CAAC,UAAAC,sBAAA,iBAAnDA,sBAAA,CAAqDrH,YAAY,EAAC,CAEvF,mBACExD,IAAA,CAACnB,GAAG,EAEF4H,EAAE,CAAE,CACF4B,CAAC,CAAE,GAAG,CACN2C,YAAY,CAAE,CAAC,CACfC,MAAM,CAAE,WAAW,CACnBT,WAAW,CAAEM,aAAa,CAAG,cAAc,CAAG,UAAU,CACxDI,eAAe,CAAEJ,aAAa,CAAG,YAAY,CAAG,aAAa,CAC7DK,UAAU,CAAE,sBACd,CAAE,CAAA7E,QAAA,cAEFpG,KAAA,CAACpB,UAAU,EACTyH,OAAO,CAAC,OAAO,CACfE,EAAE,CAAE,CACF2E,UAAU,CAAEN,aAAa,CAAG,GAAG,CAAG,GAAG,CACrCrD,KAAK,CAAEqD,aAAa,CAAG,cAAc,CAAG,cAC1C,CAAE,CAAAxE,QAAA,EAEDqE,IAAI,CAACnH,YAAY,CAAC,cAAY,CAAClD,UAAU,CAACqK,IAAI,CAACU,cAAc,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,OAC3E,CAACR,aAAa,eACZ9K,IAAA,CAACT,IAAI,EACHsI,KAAK,CAAC,SAAS,CACfC,IAAI,CAAC,OAAO,CACZL,KAAK,CAAC,SAAS,CACfhB,EAAE,CAAE,CAAE8E,EAAE,CAAE,CAAC,CAAEjE,MAAM,CAAE,EAAG,CAAE,CAC3B,CACF,EACS,CAAC,EA1BRsD,KA2BF,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,EACH,CACN,cAED5K,IAAA,CAACN,OAAO,EAAC+G,EAAE,CAAE,CAAE+E,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BtL,KAAA,CAACrB,GAAG,EAAAyH,QAAA,eACFtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,eAEtC,CAAY,CAAC,CAGZnE,gBAAgB,cACfnC,IAAA,CAACnB,GAAG,EAAC4H,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAE+E,UAAU,CAAE,QAAQ,CAAE1E,GAAG,CAAE,CAAE,CAAE,CAAAT,QAAA,cACzDtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAC,gBAAc,CAAY,CAAC,CACpD,CAAC,CACJrE,gBAAgB,cAClB/B,KAAA,CAACrB,GAAG,EAAAyH,QAAA,eACFpG,KAAA,CAACpB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,iBACX,CAAChG,UAAU,CAAC2B,gBAAgB,CAACyJ,UAAU,CAAC,CAACJ,OAAO,CAAC,CAAC,CAAC,EACxD,CAAC,cACbpL,KAAA,CAACpB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,YAChB,CAAC,MAAO,CAAAzE,QAAQ,GAAK,QAAQ,CAAG8B,QAAQ,CAAC9B,QAAQ,CAAE,EAAE,CAAC,EAAI,CAAC,CAAGA,QAAQ,EACtE,CAAC,cACb3B,KAAA,CAACpB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAACkB,KAAK,CAAC,SAAS,CAAChB,EAAE,CAAE,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,EAAC,SAC/C,CAACrE,gBAAgB,CAAC0J,qBAAqB,EACpC,CAAC,cACbzL,KAAA,CAACpB,UAAU,EAACyH,OAAO,CAAC,SAAS,CAACkB,KAAK,CAAC,gBAAgB,CAAChB,EAAE,CAAE,CAAEsB,EAAE,CAAE,CAAC,CAAErB,OAAO,CAAE,OAAQ,CAAE,CAAAJ,QAAA,EAAC,6BACzD,CAACrE,gBAAgB,CAACiG,oBAAoB,CAAC,OACpE,EAAY,CAAC,EACV,CAAC,CACJ,CAAC,IAAM,CACP,KAAM,CAAAxE,eAAe,CAAG,MAAO,CAAA7B,QAAQ,GAAK,QAAQ,CAAG8B,QAAQ,CAAC9B,QAAQ,CAAE,EAAE,CAAC,CAAGA,QAAQ,CACxF,KAAM,CAAA+B,MAAM,CAAGvC,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEmC,YAAY,EAAInC,eAAe,CAACmC,YAAY,CAAG,CAAC,CAAGnC,eAAe,CAACmC,YAAY,CAAG,CAAC,CACnH,MAAO,CAAC9C,KAAK,CAACgD,eAAe,CAAC,EAAIA,eAAe,EAAIE,MAAM,CAC7D,CAAC,EAAE,CAAC,cACJ5D,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAAC,oBAEnD,CAAY,CAAC,cAEbpG,KAAA,CAACpB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,OAAO,CAAAnB,QAAA,EAAC,0CACA,CAACjF,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEmC,YAAY,EAAInC,eAAe,CAACmC,YAAY,CAAG,CAAC,CAAGnC,eAAe,CAACmC,YAAY,CAAG,CAAC,CAAC,GAChJ,EAAY,CACb,EACE,CAAC,EACD,CAAC,CACL,CAAC,EACH,CACN,EACE,CAAC,CAGV,IAAK,EAAC,CACJ,mBACEtD,KAAA,CAACrB,GAAG,EAAAyH,QAAA,eACFtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,sBAEtC,CAAY,CAAC,cACbtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,gBAAgB,CAAChB,EAAE,CAAE,CAAEuB,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,CAAC,kHAElE,CAAY,CAAC,CAEZjE,YAAY,cACXrC,IAAA,CAACF,aAAa,EACZ8L,QAAQ,cACN5L,IAAA,CAACR,KAAK,EAACqM,QAAQ,CAAC,OAAO,CAACpF,EAAE,CAAE,CAAEuB,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,CAAC,8EAEvC,CAAO,CACR,CAAAA,QAAA,cAEDtG,IAAA,CAACH,UAAU,EACTiM,OAAO,CAAEzJ,YAAY,CAACyB,EAAG,CACzBiI,eAAe,CAAEjG,mBAAoB,CACrCkG,OAAO,CAAE/F,qBAAsB,CAChC,CAAC,CACW,CAAC,cAEhBjG,IAAA,CAACR,KAAK,EAACqM,QAAQ,CAAC,SAAS,CAACpF,EAAE,CAAE,CAAEuB,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,CAAC,gCAEzC,CAAO,CACR,EACE,CAAC,CAGV,IAAK,EAAC,CACJ,mBACEpG,KAAA,CAACrB,GAAG,EAAAyH,QAAA,eACFtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,mBAEtC,CAAY,CAAC,CAEZjE,YAAY,eACXnC,KAAA,CAACrB,GAAG,EAAC4H,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,mBAAmB,CAAE,CAAEC,EAAE,CAAE,KAAK,CAAEE,EAAE,CAAE,SAAU,CAAC,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAT,QAAA,eAEtFpG,KAAA,CAACf,KAAK,EAACsH,EAAE,CAAE,CAAE4B,CAAC,CAAE,CAAE,CAAE,CAAA/B,QAAA,eAClBtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,eAEtC,CAAY,CAAC,cACbpG,KAAA,CAACpB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,gBAAgB,CAACjB,YAAY,MAAAF,QAAA,EAAC,SACvD,CAACjE,YAAY,CAAC4J,YAAY,EACvB,CAAC,CAEZ5K,eAAe,eACdnB,KAAA,CAACrB,GAAG,EAAC4H,EAAE,CAAE,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,eACjBtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,WAAW,CAAAD,QAAA,CAAC,UAAQ,CAAY,CAAC,cACrDtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAEjF,eAAe,CAACmG,IAAI,CAAa,CAAC,cAE/DxH,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,WAAW,CAACE,EAAE,CAAE,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,CAAC,WAAS,CAAY,CAAC,cACrEtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAE,MAAO,CAAAzE,QAAQ,GAAK,QAAQ,CAAG8B,QAAQ,CAAC9B,QAAQ,CAAE,EAAE,CAAC,EAAI,CAAC,CAAGA,QAAQ,CAAa,CAAC,CAE/GI,gBAAgB,eACf/B,KAAA,CAAAE,SAAA,EAAAkG,QAAA,eACEtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,WAAW,CAACE,EAAE,CAAE,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,CAAC,cAAY,CAAY,CAAC,cACxEtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAACkB,KAAK,CAAC,SAAS,CAAAnB,QAAA,CACrCrE,gBAAgB,CAAC0J,qBAAqB,CAC7B,CAAC,EACb,CACH,cAED3L,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,WAAW,CAACE,EAAE,CAAE,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,CAAC,SAAO,CAAY,CAAC,cACnEtG,IAAA,CAACT,IAAI,EACHsI,KAAK,CAAExF,YAAY,CAAC6J,MAAM,EAAI,SAAU,CACxCzE,KAAK,CAAC,SAAS,CACfK,IAAI,CAAC,OAAO,CACb,CAAC,EACC,CACN,EACI,CAAC,cAGR5H,KAAA,CAACf,KAAK,EAACsH,EAAE,CAAE,CAAE4B,CAAC,CAAE,CAAE,CAAE,CAAA/B,QAAA,eAClBpG,KAAA,CAACpB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,EAAC,kBACpB,CAAC/D,aAAa,CAACwI,MAAM,CAAC,GACxC,EAAY,CAAC,CAEZxI,aAAa,CAACwI,MAAM,CAAG,CAAC,cACvB/K,IAAA,CAACnB,GAAG,EAAAyH,QAAA,CACD/D,aAAa,CAACyE,GAAG,CAAC,CAACmF,IAAI,CAAEvB,KAAK,gBAC7B1K,KAAA,CAACrB,GAAG,EAAe4H,EAAE,CAAE,CAAEuB,EAAE,CAAE,CAAC,CAAEK,CAAC,CAAE,CAAC,CAAE+D,OAAO,CAAE,SAAS,CAAEpB,YAAY,CAAE,CAAE,CAAE,CAAA1E,QAAA,eAC1EtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,OAAO,CAAC8F,MAAM,MAAA/F,QAAA,CAC/B6F,IAAI,CAACG,aAAa,CACT,CAAC,cACbpM,KAAA,CAACpB,UAAU,EAACyH,OAAO,CAAC,SAAS,CAACkB,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,EACjD6F,IAAI,CAACI,mBAAmB,CAAC,UAAG,CAACJ,IAAI,CAACK,eAAe,EACxC,CAAC,GANLL,IAAI,CAACrI,EAOV,CACN,CAAC,CACC,CAAC,cAEN9D,IAAA,CAACR,KAAK,EAACqM,QAAQ,CAAC,SAAS,CAAAvF,QAAA,CAAC,+EAE1B,CAAO,CACR,EACI,CAAC,cAGRtG,IAAA,CAACnB,GAAG,EAAC4H,EAAE,CAAE,CAAEgG,UAAU,CAAE,QAAQ,CAAEpC,SAAS,CAAE,QAAQ,CAAEtC,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,cAC5DtG,IAAA,CAACd,MAAM,EACLqH,OAAO,CAAC,WAAW,CACnBuB,IAAI,CAAC,OAAO,CACZX,OAAO,CAAEjB,iBAAkB,CAC3BwG,QAAQ,CAAEjK,eAAgB,CAAA6D,QAAA,CAEzB7D,eAAe,CAAG,eAAe,CAAG,cAAc,CAC7C,CAAC,CACN,CAAC,EACH,CACN,EACE,CAAC,CAGV,QACE,MAAO,cAAc,CACzB,CACF,CAAC,CAED,GAAIhB,OAAO,CAAE,CACX,mBACEzB,IAAA,CAACnB,GAAG,EAAC6H,OAAO,CAAC,MAAM,CAACiG,cAAc,CAAC,QAAQ,CAAClB,UAAU,CAAC,QAAQ,CAACmB,SAAS,CAAC,OAAO,CAAAtG,QAAA,cAC/EtG,IAAA,CAAClB,UAAU,EAAAwH,QAAA,CAAC,YAAU,CAAY,CAAC,CAChC,CAAC,CAEV,CAEA,mBACEpG,KAAA,CAACrB,GAAG,EAAAyH,QAAA,eACFtG,IAAA,CAAClB,UAAU,EAACyH,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,kBAEtC,CAAY,CAAC,CAEZ3E,KAAK,eACJ3B,IAAA,CAACR,KAAK,EAACqM,QAAQ,CAAC,OAAO,CAACpF,EAAE,CAAE,CAAEuB,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,CACnC3E,KAAK,CACD,CACR,cAEDzB,KAAA,CAACf,KAAK,EAACsH,EAAE,CAAE,CAAE4B,CAAC,CAAE,CAAE,CAAE,CAAA/B,QAAA,eAClBtG,IAAA,CAACjB,OAAO,EAAC8B,UAAU,CAAEA,UAAW,CAAC4F,EAAE,CAAE,CAAEuB,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,CAC5CjG,KAAK,CAAC2G,GAAG,CAAEa,KAAK,eACf7H,IAAA,CAAChB,IAAI,EAAAsH,QAAA,cACHtG,IAAA,CAACf,SAAS,EAAAqH,QAAA,CAAEuB,KAAK,CAAY,CAAC,EADrBA,KAEL,CACP,CAAC,CACK,CAAC,cAEV7H,IAAA,CAACnB,GAAG,EAAC4H,EAAE,CAAE,CAAEmG,SAAS,CAAE,GAAI,CAAE,CAAAtG,QAAA,CACzBH,iBAAiB,CAACtF,UAAU,CAAC,CAC3B,CAAC,cAENX,KAAA,CAACrB,GAAG,EAAC4H,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEyB,aAAa,CAAE,KAAK,CAAE0E,EAAE,CAAE,CAAE,CAAE,CAAAvG,QAAA,eACxDtG,IAAA,CAACd,MAAM,EACLuI,KAAK,CAAC,SAAS,CACfiF,QAAQ,CAAE7L,UAAU,GAAK,CAAE,CAC3BsG,OAAO,CAAEzC,UAAW,CACpB+B,EAAE,CAAE,CAAEqG,EAAE,CAAE,CAAE,CAAE,CAAAxG,QAAA,CACf,MAED,CAAQ,CAAC,cACTtG,IAAA,CAACnB,GAAG,EAAC4H,EAAE,CAAE,CAAE2B,IAAI,CAAE,UAAW,CAAE,CAAE,CAAC,CAChCvH,UAAU,CAAGR,KAAK,CAAC0K,MAAM,CAAG,CAAC,eAC5B/K,IAAA,CAACd,MAAM,EACLiI,OAAO,CAAEhD,UAAW,CACpBuI,QAAQ,CACNjL,OAAO,EACNZ,UAAU,GAAK,CAAC,EAAI,CAACM,gBAAiB,EACtCN,UAAU,GAAK,CAAC,EAAI,CAACQ,eAAgB,EACrCR,UAAU,GAAK,CAAC,EAAI,CAAC,IAAM,CAC1B,KAAM,CAAA6C,eAAe,CAAG,MAAO,CAAA7B,QAAQ,GAAK,QAAQ,CAAG8B,QAAQ,CAAC9B,QAAQ,CAAE,EAAE,CAAC,CAAGA,QAAQ,CACxF,KAAM,CAAA+B,MAAM,CAAGvC,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEmC,YAAY,EAAInC,eAAe,CAACmC,YAAY,CAAG,CAAC,CAAGnC,eAAe,CAACmC,YAAY,CAAG,CAAC,CACnH,MAAO,CAACvB,gBAAgB,EAAIvB,KAAK,CAACgD,eAAe,CAAC,EAAIA,eAAe,CAAGE,MAAM,CAChF,CAAC,EAAE,CAAE,EACJ/C,UAAU,GAAK,CAAC,EAAI,CAACwB,YACvB,CAAAiE,QAAA,CAEA7E,OAAO,CAAG,mBAAmB,CAAG,MAAM,CACjC,CACT,CACAZ,UAAU,GAAKR,KAAK,CAAC0K,MAAM,CAAG,CAAC,eAC9B/K,IAAA,CAACd,MAAM,EACLqH,OAAO,CAAC,WAAW,CACnBkB,KAAK,CAAC,SAAS,CACfN,OAAO,CAAEjB,iBAAkB,CAC3BwG,QAAQ,CAAEjK,eAAe,EAAI,CAACJ,YAAa,CAAAiE,QAAA,CAE1C7D,eAAe,CAAG,eAAe,CAAG,gBAAgB,CAC/C,CACT,EACE,CAAC,EACD,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAA9B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}