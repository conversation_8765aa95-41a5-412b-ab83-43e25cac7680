{"ast": null, "code": "import axios from'axios';const API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:8000/api';// Create axios instance\nconst api=axios.create({baseURL:API_BASE_URL,headers:{'Content-Type':'application/json','Accept':'application/json'}});// Request interceptor to add auth token\napi.interceptors.request.use(config=>{const token=localStorage.getItem('auth_token');if(token){config.headers.Authorization=`Bearer ${token}`;}return config;},error=>{return Promise.reject(error);});// Response interceptor to handle auth errors\napi.interceptors.response.use(response=>response,error=>{var _error$response;if(((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)===401){localStorage.removeItem('auth_token');localStorage.removeItem('user');window.location.href='/login';}return Promise.reject(error);});export default api;// API endpoints\nexport const endpoints={// Auth endpoints\nregister:'/register',login:'/login',logout:'/logout',forgotPassword:'/forgot-password',resetPassword:'/reset-password',verifyEmail:'/email/verify',resendVerification:'/email/verification-notification',// User endpoints\nprofile:'/user',updateProfile:'/user',uploadAvatar:'/user/avatar',adminSession:'/admin-session',// CMS endpoints\npages:'/pages',page:slug=>`/pages/${slug}`,// Credit endpoints\ncreditBalance:'/credit/balance',creditPackages:'/credit/packages',creditTransactions:'/credit/transactions',creditStatistics:'/credit/statistics',// Payment endpoints\ncreatePayment:'/payment/create',checkPaymentStatus:'/payment/status',paymentConfig:'/payment/config',billplzCallback:'/billplz/callback',// Printing endpoints\nprinting:'/printing',orders:'/orders'};", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "endpoints", "register", "login", "logout", "forgotPassword", "resetPassword", "verifyEmail", "resendVerification", "profile", "updateProfile", "uploadAvatar", "adminSession", "pages", "page", "slug", "creditBalance", "creditPackages", "creditTransactions", "creditStatistics", "createPayment", "checkPaymentStatus", "paymentConfig", "billplzCallback", "printing", "orders"], "sources": ["C:/laragon/www/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nexport default api;\n\n// API endpoints\nexport const endpoints = {\n  // Auth endpoints\n  register: '/register',\n  login: '/login',\n  logout: '/logout',\n  forgotPassword: '/forgot-password',\n  resetPassword: '/reset-password',\n  verifyEmail: '/email/verify',\n  resendVerification: '/email/verification-notification',\n\n  // User endpoints\n  profile: '/user',\n  updateProfile: '/user',\n  uploadAvatar: '/user/avatar',\n  adminSession: '/admin-session',\n\n  // CMS endpoints\n  pages: '/pages',\n  page: (slug: string) => `/pages/${slug}`,\n\n  // Credit endpoints\n  creditBalance: '/credit/balance',\n  creditPackages: '/credit/packages',\n  creditTransactions: '/credit/transactions',\n  creditStatistics: '/credit/statistics',\n\n  // Payment endpoints\n  createPayment: '/payment/create',\n  checkPaymentStatus: '/payment/status',\n  paymentConfig: '/payment/config',\n  billplzCallback: '/billplz/callback',\n\n  // Printing endpoints\n  printing: '/printing',\n  orders: '/orders',\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,KAAM,CAAAC,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,2BAA2B,CAEjF;AACA,KAAM,CAAAC,GAAG,CAAGL,KAAK,CAACM,MAAM,CAAC,CACvBC,OAAO,CAAEN,YAAY,CACrBO,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClC,QAAQ,CAAE,kBACZ,CACF,CAAC,CAAC,CAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,EAAK,CACV,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAChD,GAAIF,KAAK,CAAE,CACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,CAAG,UAAUH,KAAK,EAAE,CAClD,CACA,MAAO,CAAAD,MAAM,CACf,CAAC,CACAK,KAAK,EAAK,CACT,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,EAAKA,QAAQ,CACrBH,KAAK,EAAK,KAAAI,eAAA,CACT,GAAI,EAAAA,eAAA,CAAAJ,KAAK,CAACG,QAAQ,UAAAC,eAAA,iBAAdA,eAAA,CAAgBC,MAAM,IAAK,GAAG,CAAE,CAClCR,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC,CACrCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC,CAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,QAAQ,CACjC,CACA,MAAO,CAAAR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED,cAAe,CAAAZ,GAAG,CAElB;AACA,MAAO,MAAM,CAAAsB,SAAS,CAAG,CACvB;AACAC,QAAQ,CAAE,WAAW,CACrBC,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,SAAS,CACjBC,cAAc,CAAE,kBAAkB,CAClCC,aAAa,CAAE,iBAAiB,CAChCC,WAAW,CAAE,eAAe,CAC5BC,kBAAkB,CAAE,kCAAkC,CAEtD;AACAC,OAAO,CAAE,OAAO,CAChBC,aAAa,CAAE,OAAO,CACtBC,YAAY,CAAE,cAAc,CAC5BC,YAAY,CAAE,gBAAgB,CAE9B;AACAC,KAAK,CAAE,QAAQ,CACfC,IAAI,CAAGC,IAAY,EAAK,UAAUA,IAAI,EAAE,CAExC;AACAC,aAAa,CAAE,iBAAiB,CAChCC,cAAc,CAAE,kBAAkB,CAClCC,kBAAkB,CAAE,sBAAsB,CAC1CC,gBAAgB,CAAE,oBAAoB,CAEtC;AACAC,aAAa,CAAE,iBAAiB,CAChCC,kBAAkB,CAAE,iBAAiB,CACrCC,aAAa,CAAE,iBAAiB,CAChCC,eAAe,CAAE,mBAAmB,CAEpC;AACAC,QAAQ,CAAE,WAAW,CACrBC,MAAM,CAAE,SACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}