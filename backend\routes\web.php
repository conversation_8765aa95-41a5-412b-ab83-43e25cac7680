<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\FileUploadController;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

Route::get('/', function () {
    return view('welcome');
});

// Admin SSO route
Route::middleware(['web'])->get('/admin/sso/{token}', function ($token) {
    // Verify the SSO token
    $userId = Cache::get("admin_sso_token:{$token}");

    if (!$userId) {
        abort(403, 'Invalid or expired SSO token');
    }

    // Get the user
    $user = User::find($userId);

    if (!$user || !$user->isAdmin() || !$user->is_active) {
        abort(403, 'Unauthorized access');
    }

    // Log the user into the web session
    Auth::guard('web')->login($user);

    // Remove the used token
    Cache::forget("admin_sso_token:{$token}");

    // Redirect to admin panel
    return redirect('/admin');
})->name('admin.sso');

// Admin file download routes (for Filament admin panel)
Route::middleware(['web'])->prefix('admin')->group(function () {
    Route::get('/orders/{orderId}/files/{fileId}/download', [FileUploadController::class, 'downloadFile'])
        ->name('admin.orders.files.download');
    Route::get('/orders/{orderId}/files/download-all', [FileUploadController::class, 'downloadAllFiles'])
        ->name('admin.orders.files.download-all');
});
