[{"C:\\laragon\\www\\frontend\\src\\index.tsx": "1", "C:\\laragon\\www\\frontend\\src\\reportWebVitals.ts": "2", "C:\\laragon\\www\\frontend\\src\\App.tsx": "3", "C:\\laragon\\www\\frontend\\src\\pages\\Home.tsx": "4", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Login.tsx": "5", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Register.tsx": "6", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ForgotPassword.tsx": "7", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ResetPassword.tsx": "8", "C:\\laragon\\www\\frontend\\src\\contexts\\AuthContext.tsx": "9", "C:\\laragon\\www\\frontend\\src\\pages\\user\\Profile.tsx": "10", "C:\\laragon\\www\\frontend\\src\\pages\\user\\EditProfile.tsx": "11", "C:\\laragon\\www\\frontend\\src\\components\\layout\\Navbar.tsx": "12", "C:\\laragon\\www\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "13", "C:\\laragon\\www\\frontend\\src\\pages\\cms\\PageView.tsx": "14", "C:\\laragon\\www\\frontend\\src\\components\\auth\\EmailVerificationNotice.tsx": "15", "C:\\laragon\\www\\frontend\\src\\services\\cmsService.ts": "16", "C:\\laragon\\www\\frontend\\src\\services\\authService.ts": "17", "C:\\laragon\\www\\frontend\\src\\services\\api.ts": "18", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Settings.tsx": "19", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Dashboard.tsx": "20", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Users.tsx": "21", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardRoute.tsx": "22", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardLayout.tsx": "23", "C:\\laragon\\www\\frontend\\src\\theme\\materialDashboardTheme.ts": "24", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\ChartExample.tsx": "25", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DataTable.tsx": "26", "C:\\laragon\\www\\frontend\\src\\components\\credit\\CreditPackages.tsx": "27", "C:\\laragon\\www\\frontend\\src\\components\\credit\\TransactionHistory.tsx": "28", "C:\\laragon\\www\\frontend\\src\\components\\credit\\CreditBalance.tsx": "29", "C:\\laragon\\www\\frontend\\src\\services\\creditService.ts": "30", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Order.tsx": "31", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Orders.tsx": "32", "C:\\laragon\\www\\frontend\\src\\services\\printingService.ts": "33", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileUpload.tsx": "34", "C:\\laragon\\www\\frontend\\src\\components\\common\\ErrorBoundary.tsx": "35", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Wallet.tsx": "36", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletBalance.tsx": "37", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTopUp.tsx": "38", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTransactionHistory.tsx": "39", "C:\\laragon\\www\\frontend\\src\\theme\\dattaAbleTheme.js": "40", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleLayout.tsx": "41", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleFooter.tsx": "42", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleSidebar.tsx": "43", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleHeader.tsx": "44", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleBreadcrumbs.tsx": "45"}, {"size": 554, "mtime": 1752503120783, "results": "46", "hashOfConfig": "47"}, {"size": 425, "mtime": 1752503120799, "results": "48", "hashOfConfig": "47"}, {"size": 4223, "mtime": 1752851612759, "results": "49", "hashOfConfig": "47"}, {"size": 7912, "mtime": 1752503120830, "results": "50", "hashOfConfig": "47"}, {"size": 4298, "mtime": 1752503120814, "results": "51", "hashOfConfig": "47"}, {"size": 7734, "mtime": 1752504934229, "results": "52", "hashOfConfig": "47"}, {"size": 3447, "mtime": 1752503120814, "results": "53", "hashOfConfig": "47"}, {"size": 5805, "mtime": 1752503120814, "results": "54", "hashOfConfig": "47"}, {"size": 4114, "mtime": 1752503120767, "results": "55", "hashOfConfig": "47"}, {"size": 4077, "mtime": 1752504117348, "results": "56", "hashOfConfig": "47"}, {"size": 11533, "mtime": 1752504965088, "results": "57", "hashOfConfig": "47"}, {"size": 3660, "mtime": 1752966437120, "results": "58", "hashOfConfig": "47"}, {"size": 1355, "mtime": 1752503120861, "results": "59", "hashOfConfig": "47"}, {"size": 4072, "mtime": 1752503120830, "results": "60", "hashOfConfig": "47"}, {"size": 3494, "mtime": 1752503120861, "results": "61", "hashOfConfig": "47"}, {"size": 2009, "mtime": 1752504140096, "results": "62", "hashOfConfig": "47"}, {"size": 4610, "mtime": 1752966399879, "results": "63", "hashOfConfig": "47"}, {"size": 1884, "mtime": 1752964934301, "results": "64", "hashOfConfig": "47"}, {"size": 5100, "mtime": 1752536025085, "results": "65", "hashOfConfig": "47"}, {"size": 10443, "mtime": 1752855696829, "results": "66", "hashOfConfig": "47"}, {"size": 2464, "mtime": 1752536620414, "results": "67", "hashOfConfig": "47"}, {"size": 430, "mtime": 1752856061163, "results": "68", "hashOfConfig": "47"}, {"size": 5570, "mtime": 1752851638850, "results": "69", "hashOfConfig": "47"}, {"size": 2924, "mtime": 1752534243344, "results": "70", "hashOfConfig": "47"}, {"size": 4729, "mtime": 1752536669944, "results": "71", "hashOfConfig": "47"}, {"size": 6604, "mtime": 1752536575892, "results": "72", "hashOfConfig": "47"}, {"size": 9777, "mtime": 1752845926910, "results": "73", "hashOfConfig": "47"}, {"size": 9031, "mtime": 1752584266287, "results": "74", "hashOfConfig": "47"}, {"size": 6155, "mtime": 1752584379676, "results": "75", "hashOfConfig": "47"}, {"size": 5582, "mtime": 1752847027187, "results": "76", "hashOfConfig": "47"}, {"size": 29690, "mtime": 1752944692634, "results": "77", "hashOfConfig": "47"}, {"size": 27549, "mtime": 1752710086718, "results": "78", "hashOfConfig": "47"}, {"size": 10226, "mtime": 1752621266300, "results": "79", "hashOfConfig": "47"}, {"size": 18063, "mtime": 1752922213915, "results": "80", "hashOfConfig": "47"}, {"size": 1430, "mtime": 1752673572781, "results": "81", "hashOfConfig": "47"}, {"size": 10001, "mtime": 1752854371015, "results": "82", "hashOfConfig": "47"}, {"size": 11644, "mtime": 1752856424457, "results": "83", "hashOfConfig": "47"}, {"size": 11379, "mtime": 1752854666525, "results": "84", "hashOfConfig": "47"}, {"size": 10996, "mtime": 1752856638130, "results": "85", "hashOfConfig": "47"}, {"size": 5454, "mtime": 1752853884178, "results": "86", "hashOfConfig": "47"}, {"size": 6771, "mtime": 1752921639304, "results": "87", "hashOfConfig": "47"}, {"size": 2939, "mtime": 1752883952010, "results": "88", "hashOfConfig": "47"}, {"size": 6847, "mtime": 1752885142138, "results": "89", "hashOfConfig": "47"}, {"size": 10532, "mtime": 1752966421702, "results": "90", "hashOfConfig": "47"}, {"size": 2706, "mtime": 1752922816616, "results": "91", "hashOfConfig": "47"}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tace3p", {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\laragon\\www\\frontend\\src\\index.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\laragon\\www\\frontend\\src\\App.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\Home.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Login.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Register.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ForgotPassword.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ResetPassword.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\user\\Profile.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\user\\EditProfile.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\layout\\Navbar.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\cms\\PageView.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\auth\\EmailVerificationNotice.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\cmsService.ts", [], [], "C:\\laragon\\www\\frontend\\src\\services\\authService.ts", [], [], "C:\\laragon\\www\\frontend\\src\\services\\api.ts", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Settings.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Dashboard.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Users.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardRoute.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardLayout.tsx", ["227", "228"], [], "C:\\laragon\\www\\frontend\\src\\theme\\materialDashboardTheme.ts", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\ChartExample.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DataTable.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\credit\\CreditPackages.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\credit\\TransactionHistory.tsx", ["229"], [], "C:\\laragon\\www\\frontend\\src\\components\\credit\\CreditBalance.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\creditService.ts", ["230", "231"], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Order.tsx", ["232", "233", "234", "235", "236", "237"], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Orders.tsx", ["238", "239", "240"], [], "C:\\laragon\\www\\frontend\\src\\services\\printingService.ts", ["241"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileUpload.tsx", ["242", "243"], [], "C:\\laragon\\www\\frontend\\src\\components\\common\\ErrorBoundary.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Wallet.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletBalance.tsx", ["244"], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTopUp.tsx", ["245", "246"], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTransactionHistory.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\theme\\dattaAbleTheme.js", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleLayout.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleFooter.tsx", ["247", "248", "249", "250"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleSidebar.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleHeader.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleBreadcrumbs.tsx", ["251", "252"], [], {"ruleId": "253", "severity": 1, "message": "254", "line": 26, "column": 3, "nodeType": "255", "messageId": "256", "endLine": 26, "endColumn": 14}, {"ruleId": "253", "severity": 1, "message": "257", "line": 42, "column": 9, "nodeType": "255", "messageId": "256", "endLine": 42, "endColumn": 17}, {"ruleId": "253", "severity": 1, "message": "258", "line": 41, "column": 10, "nodeType": "255", "messageId": "256", "endLine": 41, "endColumn": 20}, {"ruleId": "253", "severity": 1, "message": "259", "line": 1, "column": 15, "nodeType": "255", "messageId": "256", "endLine": 1, "endColumn": 24}, {"ruleId": "260", "severity": 1, "message": "261", "line": 223, "column": 1, "nodeType": "262", "endLine": 223, "endColumn": 36}, {"ruleId": "253", "severity": 1, "message": "263", "line": 18, "column": 3, "nodeType": "255", "messageId": "256", "endLine": 18, "endColumn": 14}, {"ruleId": "253", "severity": 1, "message": "264", "line": 19, "column": 3, "nodeType": "255", "messageId": "256", "endLine": 19, "endColumn": 13}, {"ruleId": "253", "severity": 1, "message": "265", "line": 20, "column": 3, "nodeType": "255", "messageId": "256", "endLine": 20, "endColumn": 9}, {"ruleId": "253", "severity": 1, "message": "266", "line": 21, "column": 3, "nodeType": "255", "messageId": "256", "endLine": 21, "endColumn": 11}, {"ruleId": "253", "severity": 1, "message": "267", "line": 43, "column": 10, "nodeType": "255", "messageId": "256", "endLine": 43, "endColumn": 20}, {"ruleId": "268", "severity": 1, "message": "269", "line": 141, "column": 6, "nodeType": "270", "endLine": 141, "endColumn": 50, "suggestions": "271"}, {"ruleId": "253", "severity": 1, "message": "272", "line": 27, "column": 3, "nodeType": "255", "messageId": "256", "endLine": 27, "endColumn": 10}, {"ruleId": "253", "severity": 1, "message": "273", "line": 41, "column": 17, "nodeType": "255", "messageId": "256", "endLine": 41, "endColumn": 27}, {"ruleId": "268", "severity": 1, "message": "274", "line": 65, "column": 6, "nodeType": "270", "endLine": 65, "endColumn": 12, "suggestions": "275"}, {"ruleId": "260", "severity": 1, "message": "261", "line": 335, "column": 1, "nodeType": "262", "endLine": 335, "endColumn": 38}, {"ruleId": "268", "severity": 1, "message": "276", "line": 76, "column": 6, "nodeType": "270", "endLine": 76, "endColumn": 15, "suggestions": "277"}, {"ruleId": "268", "severity": 1, "message": "278", "line": 188, "column": 6, "nodeType": "270", "endLine": 188, "endColumn": 40, "suggestions": "279"}, {"ruleId": "253", "severity": 1, "message": "280", "line": 3, "column": 3, "nodeType": "255", "messageId": "256", "endLine": 3, "endColumn": 12}, {"ruleId": "253", "severity": 1, "message": "280", "line": 3, "column": 3, "nodeType": "255", "messageId": "256", "endLine": 3, "endColumn": 12}, {"ruleId": "253", "severity": 1, "message": "281", "line": 11, "column": 3, "nodeType": "255", "messageId": "256", "endLine": 11, "endColumn": 8}, {"ruleId": "282", "severity": 1, "message": "283", "line": 33, "column": 15, "nodeType": "284", "endLine": 42, "endColumn": 16}, {"ruleId": "282", "severity": 1, "message": "283", "line": 49, "column": 15, "nodeType": "284", "endLine": 59, "endColumn": 16}, {"ruleId": "282", "severity": 1, "message": "283", "line": 62, "column": 15, "nodeType": "284", "endLine": 72, "endColumn": 16}, {"ruleId": "282", "severity": 1, "message": "283", "line": 75, "column": 15, "nodeType": "284", "endLine": 84, "endColumn": 16}, {"ruleId": "253", "severity": 1, "message": "285", "line": 19, "column": 11, "nodeType": "255", "messageId": "256", "endLine": 19, "endColumn": 23}, {"ruleId": "253", "severity": 1, "message": "286", "line": 51, "column": 9, "nodeType": "255", "messageId": "256", "endLine": 51, "endColumn": 29}, "@typescript-eslint/no-unused-vars", "'ChevronLeft' is defined but never used.", "Identifier", "unusedVar", "'isMobile' is assigned a value but never used.", "'totalPages' is assigned a value but never used.", "'endpoints' is defined but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'orderItems' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'calculatePrice'. Either include it or remove the dependency array.", "ArrayExpression", ["287"], "'Divider' is defined but never used.", "'FilterIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadOrders'. Either include it or remove the dependency array.", ["288"], "React Hook useEffect has missing dependencies: 'loadSettings' and 'loadUploadedFiles'. Either include them or remove the dependency array.", ["289"], "React Hook useCallback has missing dependencies: 'onError', 'uploadFiles', and 'validateFiles'. Either include them or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["290"], "'Container' is defined but never used.", "'Badge' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'pathSegments' is assigned a value but never used.", "'breadcrumbItemStyles' is assigned a value but never used.", {"desc": "291", "fix": "292"}, {"desc": "293", "fix": "294"}, {"desc": "295", "fix": "296"}, {"desc": "297", "fix": "298"}, "Update the dependencies array to be: [selectedProduct, quantity, selectedOptions, calculatePrice]", {"range": "299", "text": "300"}, "Update the dependencies array to be: [loadOrders, page]", {"range": "301", "text": "302"}, "Update the dependencies array to be: [loadSettings, loadUploadedFiles, orderId]", {"range": "303", "text": "304"}, "Update the dependencies array to be: [onError, orderId, uploadFiles, validateFiles]", {"range": "305", "text": "306"}, [4820, 4864], "[selectedProduct, quantity, selectedOptions, calculatePrice]", [1616, 1622], "[loadOrders, page]", [2001, 2010], "[loadSettings, loadUploadedFiles, orderId]", [5634, 5668], "[onError, orderId, uploadFiles, validateFiles]"]