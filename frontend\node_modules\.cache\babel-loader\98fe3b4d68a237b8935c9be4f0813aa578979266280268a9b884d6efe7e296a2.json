{"ast": null, "code": "import api, { endpoints } from './api';\nclass AuthService {\n  // Register new user\n  async register(data) {\n    const response = await api.post(endpoints.register, data);\n    const {\n      user,\n      token\n    } = response.data;\n    if (token) {\n      this.setAuthData(user, token);\n    }\n    return response.data;\n  }\n\n  // Login user\n  async login(credentials) {\n    const response = await api.post(endpoints.login, credentials);\n    const {\n      user,\n      token\n    } = response.data;\n    if (token) {\n      this.setAuthData(user, token);\n    }\n    return response.data;\n  }\n\n  // Logout user\n  async logout() {\n    try {\n      await api.post(endpoints.logout);\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      this.clearAuthData();\n    }\n  }\n\n  // Get current user profile\n  async getProfile() {\n    const response = await api.get(endpoints.profile);\n    return response.data.user;\n  }\n\n  // Update user profile\n  async updateProfile(data) {\n    const response = await api.put(endpoints.updateProfile, data);\n    const user = response.data.user;\n\n    // Update stored user data\n    localStorage.setItem('user', JSON.stringify(user));\n    return user;\n  }\n\n  // Upload avatar\n  async uploadAvatar(file) {\n    const formData = new FormData();\n    formData.append('avatar', file);\n    const response = await api.post(endpoints.uploadAvatar, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    const user = response.data.user;\n    localStorage.setItem('user', JSON.stringify(user));\n    return user;\n  }\n\n  // Forgot password\n  async forgotPassword(email) {\n    const response = await api.post(endpoints.forgotPassword, {\n      email\n    });\n    return response.data;\n  }\n\n  // Reset password\n  async resetPassword(data) {\n    const response = await api.post(endpoints.resetPassword, data);\n    return response.data;\n  }\n\n  // Resend email verification\n  async resendVerification() {\n    const response = await api.post(endpoints.resendVerification);\n    return response.data;\n  }\n\n  // Create admin session and navigate to admin panel\n  async navigateToAdminPanel() {\n    try {\n      const response = await api.post(endpoints.adminSession);\n      if (response.data.success) {\n        // Open SSO URL in new tab - this will create the web session and redirect to admin\n        const ssoUrl = response.data.admin_url;\n        window.open(ssoUrl, '_blank');\n      } else {\n        throw new Error(response.data.message || 'Failed to create admin session');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      throw new Error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to access admin panel');\n    }\n  }\n\n  // Helper methods\n  setAuthData(user, token) {\n    localStorage.setItem('auth_token', token);\n    localStorage.setItem('user', JSON.stringify(user));\n  }\n  clearAuthData() {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('user');\n  }\n  getStoredUser() {\n    const userStr = localStorage.getItem('user');\n    return userStr ? JSON.parse(userStr) : null;\n  }\n  getStoredToken() {\n    return localStorage.getItem('auth_token');\n  }\n  isAuthenticated() {\n    return !!this.getStoredToken();\n  }\n  isEmailVerified() {\n    const user = this.getStoredUser();\n    return !!(user !== null && user !== void 0 && user.email_verified_at);\n  }\n}\nconst authService = new AuthService();\nexport default authService;", "map": {"version": 3, "names": ["api", "endpoints", "AuthService", "register", "data", "response", "post", "user", "token", "setAuthData", "login", "credentials", "logout", "error", "console", "clearAuthData", "getProfile", "get", "profile", "updateProfile", "put", "localStorage", "setItem", "JSON", "stringify", "uploadAvatar", "file", "formData", "FormData", "append", "headers", "forgotPassword", "email", "resetPassword", "resendVerification", "navigateToAdminPanel", "adminSession", "success", "ssoUrl", "admin_url", "window", "open", "Error", "message", "_error$response", "_error$response$data", "removeItem", "getStoredUser", "userStr", "getItem", "parse", "getStoredToken", "isAuthenticated", "isEmailVerified", "email_verified_at", "authService"], "sources": ["C:/laragon/www/frontend/src/services/authService.ts"], "sourcesContent": ["import api, { endpoints } from './api';\n\nexport interface User {\n  id: number;\n  name: string;\n  email: string;\n  phone?: string;\n  bio?: string;\n  avatar?: string;\n  date_of_birth?: string;\n  role: 'admin' | 'user';\n  is_active: boolean;\n  email_verified_at?: string;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  name: string;\n  email: string;\n  password: string;\n  password_confirmation: string;\n  phone?: string;\n  date_of_birth?: string;\n}\n\nexport interface AuthResponse {\n  message: string;\n  user: User;\n  token: string;\n}\n\nexport interface UpdateProfileData {\n  name?: string;\n  email?: string;\n  phone?: string;\n  bio?: string;\n  date_of_birth?: string;\n  current_password?: string;\n  password?: string;\n  password_confirmation?: string;\n}\n\nclass AuthService {\n  // Register new user\n  async register(data: RegisterData): Promise<AuthResponse> {\n    const response = await api.post(endpoints.register, data);\n    const { user, token } = response.data;\n\n    if (token) {\n      this.setAuthData(user, token);\n    }\n\n    return response.data;\n  }\n\n  // Login user\n  async login(credentials: LoginCredentials): Promise<AuthResponse> {\n    const response = await api.post(endpoints.login, credentials);\n    const { user, token } = response.data;\n\n    if (token) {\n      this.setAuthData(user, token);\n    }\n\n    return response.data;\n  }\n\n  // Logout user\n  async logout(): Promise<void> {\n    try {\n      await api.post(endpoints.logout);\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      this.clearAuthData();\n    }\n  }\n\n  // Get current user profile\n  async getProfile(): Promise<User> {\n    const response = await api.get(endpoints.profile);\n    return response.data.user;\n  }\n\n  // Update user profile\n  async updateProfile(data: UpdateProfileData): Promise<User> {\n    const response = await api.put(endpoints.updateProfile, data);\n    const user = response.data.user;\n\n    // Update stored user data\n    localStorage.setItem('user', JSON.stringify(user));\n\n    return user;\n  }\n\n  // Upload avatar\n  async uploadAvatar(file: File): Promise<User> {\n    const formData = new FormData();\n    formData.append('avatar', file);\n\n    const response = await api.post(endpoints.uploadAvatar, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n\n    const user = response.data.user;\n    localStorage.setItem('user', JSON.stringify(user));\n\n    return user;\n  }\n\n  // Forgot password\n  async forgotPassword(email: string): Promise<{ message: string }> {\n    const response = await api.post(endpoints.forgotPassword, { email });\n    return response.data;\n  }\n\n  // Reset password\n  async resetPassword(data: {\n    token: string;\n    email: string;\n    password: string;\n    password_confirmation: string;\n  }): Promise<{ message: string }> {\n    const response = await api.post(endpoints.resetPassword, data);\n    return response.data;\n  }\n\n  // Resend email verification\n  async resendVerification(): Promise<{ message: string }> {\n    const response = await api.post(endpoints.resendVerification);\n    return response.data;\n  }\n\n  // Create admin session and navigate to admin panel\n  async navigateToAdminPanel(): Promise<void> {\n    try {\n      const response = await api.post(endpoints.adminSession);\n      if (response.data.success) {\n        // Open SSO URL in new tab - this will create the web session and redirect to admin\n        const ssoUrl = response.data.admin_url;\n        window.open(ssoUrl, '_blank');\n      } else {\n        throw new Error(response.data.message || 'Failed to create admin session');\n      }\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to access admin panel');\n    }\n  }\n\n  // Helper methods\n  setAuthData(user: User, token: string): void {\n    localStorage.setItem('auth_token', token);\n    localStorage.setItem('user', JSON.stringify(user));\n  }\n\n  clearAuthData(): void {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('user');\n  }\n\n  getStoredUser(): User | null {\n    const userStr = localStorage.getItem('user');\n    return userStr ? JSON.parse(userStr) : null;\n  }\n\n  getStoredToken(): string | null {\n    return localStorage.getItem('auth_token');\n  }\n\n  isAuthenticated(): boolean {\n    return !!this.getStoredToken();\n  }\n\n  isEmailVerified(): boolean {\n    const user = this.getStoredUser();\n    return !!user?.email_verified_at;\n  }\n}\n\nconst authService = new AuthService();\nexport default authService;\n"], "mappings": "AAAA,OAAOA,GAAG,IAAIC,SAAS,QAAQ,OAAO;AAgDtC,MAAMC,WAAW,CAAC;EAChB;EACA,MAAMC,QAAQA,CAACC,IAAkB,EAAyB;IACxD,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAACL,SAAS,CAACE,QAAQ,EAAEC,IAAI,CAAC;IACzD,MAAM;MAAEG,IAAI;MAAEC;IAAM,CAAC,GAAGH,QAAQ,CAACD,IAAI;IAErC,IAAII,KAAK,EAAE;MACT,IAAI,CAACC,WAAW,CAACF,IAAI,EAAEC,KAAK,CAAC;IAC/B;IAEA,OAAOH,QAAQ,CAACD,IAAI;EACtB;;EAEA;EACA,MAAMM,KAAKA,CAACC,WAA6B,EAAyB;IAChE,MAAMN,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAACL,SAAS,CAACS,KAAK,EAAEC,WAAW,CAAC;IAC7D,MAAM;MAAEJ,IAAI;MAAEC;IAAM,CAAC,GAAGH,QAAQ,CAACD,IAAI;IAErC,IAAII,KAAK,EAAE;MACT,IAAI,CAACC,WAAW,CAACF,IAAI,EAAEC,KAAK,CAAC;IAC/B;IAEA,OAAOH,QAAQ,CAACD,IAAI;EACtB;;EAEA;EACA,MAAMQ,MAAMA,CAAA,EAAkB;IAC5B,IAAI;MACF,MAAMZ,GAAG,CAACM,IAAI,CAACL,SAAS,CAACW,MAAM,CAAC;IAClC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACR,IAAI,CAACE,aAAa,CAAC,CAAC;IACtB;EACF;;EAEA;EACA,MAAMC,UAAUA,CAAA,EAAkB;IAChC,MAAMX,QAAQ,GAAG,MAAML,GAAG,CAACiB,GAAG,CAAChB,SAAS,CAACiB,OAAO,CAAC;IACjD,OAAOb,QAAQ,CAACD,IAAI,CAACG,IAAI;EAC3B;;EAEA;EACA,MAAMY,aAAaA,CAACf,IAAuB,EAAiB;IAC1D,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACoB,GAAG,CAACnB,SAAS,CAACkB,aAAa,EAAEf,IAAI,CAAC;IAC7D,MAAMG,IAAI,GAAGF,QAAQ,CAACD,IAAI,CAACG,IAAI;;IAE/B;IACAc,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI,CAAC,CAAC;IAElD,OAAOA,IAAI;EACb;;EAEA;EACA,MAAMkB,YAAYA,CAACC,IAAU,EAAiB;IAC5C,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEH,IAAI,CAAC;IAE/B,MAAMrB,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAACL,SAAS,CAACwB,YAAY,EAAEE,QAAQ,EAAE;MAChEG,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,MAAMvB,IAAI,GAAGF,QAAQ,CAACD,IAAI,CAACG,IAAI;IAC/Bc,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI,CAAC,CAAC;IAElD,OAAOA,IAAI;EACb;;EAEA;EACA,MAAMwB,cAAcA,CAACC,KAAa,EAAgC;IAChE,MAAM3B,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAACL,SAAS,CAAC8B,cAAc,EAAE;MAAEC;IAAM,CAAC,CAAC;IACpE,OAAO3B,QAAQ,CAACD,IAAI;EACtB;;EAEA;EACA,MAAM6B,aAAaA,CAAC7B,IAKnB,EAAgC;IAC/B,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAACL,SAAS,CAACgC,aAAa,EAAE7B,IAAI,CAAC;IAC9D,OAAOC,QAAQ,CAACD,IAAI;EACtB;;EAEA;EACA,MAAM8B,kBAAkBA,CAAA,EAAiC;IACvD,MAAM7B,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAACL,SAAS,CAACiC,kBAAkB,CAAC;IAC7D,OAAO7B,QAAQ,CAACD,IAAI;EACtB;;EAEA;EACA,MAAM+B,oBAAoBA,CAAA,EAAkB;IAC1C,IAAI;MACF,MAAM9B,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAACL,SAAS,CAACmC,YAAY,CAAC;MACvD,IAAI/B,QAAQ,CAACD,IAAI,CAACiC,OAAO,EAAE;QACzB;QACA,MAAMC,MAAM,GAAGjC,QAAQ,CAACD,IAAI,CAACmC,SAAS;QACtCC,MAAM,CAACC,IAAI,CAACH,MAAM,EAAE,QAAQ,CAAC;MAC/B,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAACrC,QAAQ,CAACD,IAAI,CAACuC,OAAO,IAAI,gCAAgC,CAAC;MAC5E;IACF,CAAC,CAAC,OAAO9B,KAAU,EAAE;MAAA,IAAA+B,eAAA,EAAAC,oBAAA;MACnB,MAAM,IAAIH,KAAK,CAAC,EAAAE,eAAA,GAAA/B,KAAK,CAACR,QAAQ,cAAAuC,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBxC,IAAI,cAAAyC,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,8BAA8B,CAAC;IAClF;EACF;;EAEA;EACAlC,WAAWA,CAACF,IAAU,EAAEC,KAAa,EAAQ;IAC3Ca,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEd,KAAK,CAAC;IACzCa,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI,CAAC,CAAC;EACpD;EAEAQ,aAAaA,CAAA,EAAS;IACpBM,YAAY,CAACyB,UAAU,CAAC,YAAY,CAAC;IACrCzB,YAAY,CAACyB,UAAU,CAAC,MAAM,CAAC;EACjC;EAEAC,aAAaA,CAAA,EAAgB;IAC3B,MAAMC,OAAO,GAAG3B,YAAY,CAAC4B,OAAO,CAAC,MAAM,CAAC;IAC5C,OAAOD,OAAO,GAAGzB,IAAI,CAAC2B,KAAK,CAACF,OAAO,CAAC,GAAG,IAAI;EAC7C;EAEAG,cAAcA,CAAA,EAAkB;IAC9B,OAAO9B,YAAY,CAAC4B,OAAO,CAAC,YAAY,CAAC;EAC3C;EAEAG,eAAeA,CAAA,EAAY;IACzB,OAAO,CAAC,CAAC,IAAI,CAACD,cAAc,CAAC,CAAC;EAChC;EAEAE,eAAeA,CAAA,EAAY;IACzB,MAAM9C,IAAI,GAAG,IAAI,CAACwC,aAAa,CAAC,CAAC;IACjC,OAAO,CAAC,EAACxC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+C,iBAAiB;EAClC;AACF;AAEA,MAAMC,WAAW,GAAG,IAAIrD,WAAW,CAAC,CAAC;AACrC,eAAeqD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}