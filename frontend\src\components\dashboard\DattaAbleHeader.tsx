import React, { useState } from 'react';
import { Navbar, Nav, Dropdown, Button } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import authService from '../../services/authService';
import dattaAbleTheme from '../../theme/dattaAbleTheme';

interface DattaAbleHeaderProps {
  onToggleSidebar: () => void;
  onToggleSidebarCollapse: () => void;
  sidebarCollapsed: boolean;
}

const DattaAbleHeader: React.FC<DattaAbleHeaderProps> = ({ onToggleSidebar, onToggleSidebarCollapse, sidebarCollapsed }) => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  const handleAdminPanel = async () => {
    try {
      await authService.navigateToAdminPanel();
    } catch (error: any) {
      console.error('Failed to access admin panel:', error);
      // Fallback to direct navigation
      const adminUrl = process.env.REACT_APP_API_URL?.replace('/api', '/admin') || 'http://localhost:8000/admin';
      window.open(adminUrl, '_blank');
    }
  };

  const headerStyles: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    right: 0,
    left: sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width,
    height: dattaAbleTheme.layout.header.height,
    backgroundColor: dattaAbleTheme.colors.background.paper,
    borderBottom: `1px solid ${dattaAbleTheme.colors.border}`,
    boxShadow: dattaAbleTheme.shadows.sm,
    zIndex: 1030,
    transition: 'left 0.3s ease',
    padding: 0,
  };

  const mobileHeaderStyles: React.CSSProperties = {
    ...headerStyles,
    left: 0,
  };

  const navbarStyles: React.CSSProperties = {
    height: '100%',
    padding: `0 ${dattaAbleTheme.spacing[4]}`,
  };

  const toggleButtonStyles: React.CSSProperties = {
    backgroundColor: 'transparent',
    border: 'none',
    color: dattaAbleTheme.colors.text.primary,
    fontSize: '1.25rem',
    padding: dattaAbleTheme.spacing[2],
    borderRadius: dattaAbleTheme.borderRadius.md,
    transition: 'all 0.2s ease',
  };

  const userAvatarStyles: React.CSSProperties = {
    width: '32px',
    height: '32px',
    borderRadius: '50%',
    backgroundColor: dattaAbleTheme.colors.primary.main,
    color: 'white',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '0.875rem',
    fontWeight: dattaAbleTheme.typography.fontWeight.medium,
  };

  const getUserInitials = (name: string | undefined): string => {
    if (!name) return 'U';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  return (
    <div style={window.innerWidth < 768 ? mobileHeaderStyles : headerStyles}>
      <Navbar expand="lg" style={navbarStyles} className="px-0">
        <div className="d-flex align-items-center">
          {/* Mobile Menu Toggle */}
          <Button
            style={toggleButtonStyles}
            onClick={onToggleSidebar}
            className="d-lg-none me-2"
            onMouseEnter={(e) => {
              (e.target as HTMLElement).style.backgroundColor = dattaAbleTheme.colors.background.light;
            }}
            onMouseLeave={(e) => {
              (e.target as HTMLElement).style.backgroundColor = 'transparent';
            }}
          >
            <i className="fas fa-bars"></i>
          </Button>

          {/* Desktop Sidebar Toggle */}
          <Button
            style={toggleButtonStyles}
            onClick={onToggleSidebarCollapse}
            className="d-none d-lg-block me-3"
            onMouseEnter={(e) => {
              (e.target as HTMLElement).style.backgroundColor = dattaAbleTheme.colors.background.light;
            }}
            onMouseLeave={(e) => {
              (e.target as HTMLElement).style.backgroundColor = 'transparent';
            }}
          >
            <i className={`fas ${sidebarCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'}`}></i>
          </Button>

          {/* Page Title */}
          <h5 className="mb-0 text-dark fw-semibold">
            Dashboard
          </h5>
        </div>

        {/* Right Side Navigation */}
        <Nav className="ms-auto d-flex align-items-center">
          {/* Notifications */}
          <Dropdown className="me-3">
            <Dropdown.Toggle
              as="button"
              style={{
                ...toggleButtonStyles,
                position: 'relative',
              }}
              className="position-relative"
            >
              <i className="fas fa-bell"></i>
              <span
                style={{
                  position: 'absolute',
                  top: '4px',
                  right: '4px',
                  width: '8px',
                  height: '8px',
                  backgroundColor: dattaAbleTheme.colors.error.main,
                  borderRadius: '50%',
                }}
              ></span>
            </Dropdown.Toggle>
            <Dropdown.Menu align="end" style={{ minWidth: '300px' }}>
              <Dropdown.Header>Notifications</Dropdown.Header>
              <Dropdown.Item>
                <div className="d-flex">
                  <div className="flex-shrink-0">
                    <div
                      style={{
                        width: '32px',
                        height: '32px',
                        backgroundColor: dattaAbleTheme.colors.success.main,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <i className="fas fa-check text-white"></i>
                    </div>
                  </div>
                  <div className="flex-grow-1 ms-3">
                    <h6 className="mb-1">Welcome to Dashboard</h6>
                    <p className="mb-0 text-muted small">
                      Your dashboard is ready to use
                    </p>
                  </div>
                </div>
              </Dropdown.Item>
              <Dropdown.Divider />
              <Dropdown.Item className="text-center">
                <small>View all notifications</small>
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>

          {/* User Profile Dropdown */}
          <Dropdown show={showProfileDropdown} onToggle={setShowProfileDropdown}>
            <Dropdown.Toggle
              as="div"
              style={{ cursor: 'pointer' }}
              className="d-flex align-items-center"
            >
              <div style={userAvatarStyles}>
                {getUserInitials(user?.name)}
              </div>
              <div className="ms-2 d-none d-sm-block">
                <div
                  style={{
                    fontSize: '0.875rem',
                    fontWeight: dattaAbleTheme.typography.fontWeight.medium,
                    color: dattaAbleTheme.colors.text.primary,
                  }}
                >
                  {user?.name || 'User'}
                </div>
                <div
                  style={{
                    fontSize: '0.75rem',
                    color: dattaAbleTheme.colors.text.secondary,
                  }}
                >
                  {user?.email || '<EMAIL>'}
                </div>
              </div>
              <i className="fas fa-chevron-down ms-2 text-muted"></i>
            </Dropdown.Toggle>

            <Dropdown.Menu align="end" style={{ minWidth: '200px' }}>
              <Dropdown.Header>
                <div className="text-center">
                  <div style={userAvatarStyles} className="mx-auto mb-2">
                    {getUserInitials(user?.name)}
                  </div>
                  <h6 className="mb-0">{user?.name || 'User'}</h6>
                  <small className="text-muted">{user?.email || '<EMAIL>'}</small>
                </div>
              </Dropdown.Header>
              <Dropdown.Divider />
              <Dropdown.Item onClick={() => navigate('/profile')}>
                <i className="fas fa-user me-2"></i>
                Profile
              </Dropdown.Item>
              <Dropdown.Item onClick={() => navigate('/profile/edit')}>
                <i className="fas fa-cog me-2"></i>
                Settings
              </Dropdown.Item>
              {user?.role === 'admin' && (
                <>
                  <Dropdown.Divider />
                  <Dropdown.Item onClick={handleAdminPanel}>
                    <i className="fas fa-shield-alt me-2"></i>
                    Admin Panel
                  </Dropdown.Item>
                </>
              )}
              <Dropdown.Divider />
              <Dropdown.Item onClick={handleLogout} className="text-danger">
                <i className="fas fa-sign-out-alt me-2"></i>
                Logout
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </Nav>
      </Navbar>

      {/* Font Awesome Icons */}
      <link
        rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      />

      <style>{`
        .dropdown-toggle::after {
          display: none;
        }

        .dropdown-menu {
          border: 1px solid ${dattaAbleTheme.colors.border};
          border-radius: ${dattaAbleTheme.borderRadius.lg};
          box-shadow: ${dattaAbleTheme.shadows.lg};
          padding: ${dattaAbleTheme.spacing[2]};
        }

        .dropdown-item {
          border-radius: ${dattaAbleTheme.borderRadius.md};
          padding: ${dattaAbleTheme.spacing[2]} ${dattaAbleTheme.spacing[3]};
          transition: all 0.2s ease;
        }

        .dropdown-item:hover {
          background-color: ${dattaAbleTheme.colors.background.light};
          color: ${dattaAbleTheme.colors.text.primary};
        }

        .dropdown-header {
          padding: ${dattaAbleTheme.spacing[3]};
          font-weight: ${dattaAbleTheme.typography.fontWeight.semibold};
          color: ${dattaAbleTheme.colors.text.primary};
        }

        @media (max-width: 767.98px) {
          .header-mobile {
            left: 0 !important;
          }
        }
      `}</style>
    </div>
  );
};

export default DattaAbleHeader;
