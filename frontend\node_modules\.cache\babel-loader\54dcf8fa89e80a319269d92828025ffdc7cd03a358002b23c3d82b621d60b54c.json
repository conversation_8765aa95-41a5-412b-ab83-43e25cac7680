{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DattaAbleHeader.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Navbar, Nav, Dropdown, Button } from 'react-bootstrap';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport dattaAbleTheme from '../../theme/dattaAbleTheme';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DattaAbleHeader = ({\n  onToggleSidebar,\n  onToggleSidebarCollapse,\n  sidebarCollapsed\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [showProfileDropdown, setShowProfileDropdown] = useState(false);\n  const handleLogout = async () => {\n    await logout();\n    navigate('/login');\n  };\n  const handleAdminPanel = () => {\n    var _process$env$REACT_AP;\n    // Open admin panel in new tab to preserve current session\n    const adminUrl = ((_process$env$REACT_AP = process.env.REACT_APP_API_URL) === null || _process$env$REACT_AP === void 0 ? void 0 : _process$env$REACT_AP.replace('/api', '/admin')) || 'http://localhost:8000/admin';\n    window.open(adminUrl, '_blank');\n  };\n  const headerStyles = {\n    position: 'fixed',\n    top: 0,\n    right: 0,\n    left: sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width,\n    height: dattaAbleTheme.layout.header.height,\n    backgroundColor: dattaAbleTheme.colors.background.paper,\n    borderBottom: `1px solid ${dattaAbleTheme.colors.border}`,\n    boxShadow: dattaAbleTheme.shadows.sm,\n    zIndex: 1030,\n    transition: 'left 0.3s ease',\n    padding: 0\n  };\n  const mobileHeaderStyles = {\n    ...headerStyles,\n    left: 0\n  };\n  const navbarStyles = {\n    height: '100%',\n    padding: `0 ${dattaAbleTheme.spacing[4]}`\n  };\n  const toggleButtonStyles = {\n    backgroundColor: 'transparent',\n    border: 'none',\n    color: dattaAbleTheme.colors.text.primary,\n    fontSize: '1.25rem',\n    padding: dattaAbleTheme.spacing[2],\n    borderRadius: dattaAbleTheme.borderRadius.md,\n    transition: 'all 0.2s ease'\n  };\n  const userAvatarStyles = {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    backgroundColor: dattaAbleTheme.colors.primary.main,\n    color: 'white',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontSize: '0.875rem',\n    fontWeight: dattaAbleTheme.typography.fontWeight.medium\n  };\n  const getUserInitials = name => {\n    if (!name) return 'U';\n    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: window.innerWidth < 768 ? mobileHeaderStyles : headerStyles,\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {\n      expand: \"lg\",\n      style: navbarStyles,\n      className: \"px-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          style: toggleButtonStyles,\n          onClick: onToggleSidebar,\n          className: \"d-lg-none me-2\",\n          onMouseEnter: e => {\n            e.target.style.backgroundColor = dattaAbleTheme.colors.background.light;\n          },\n          onMouseLeave: e => {\n            e.target.style.backgroundColor = 'transparent';\n          },\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-bars\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          style: toggleButtonStyles,\n          onClick: onToggleSidebarCollapse,\n          className: \"d-none d-lg-block me-3\",\n          onMouseEnter: e => {\n            e.target.style.backgroundColor = dattaAbleTheme.colors.background.light;\n          },\n          onMouseLeave: e => {\n            e.target.style.backgroundColor = 'transparent';\n          },\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: `fas ${sidebarCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mb-0 text-dark fw-semibold\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Nav, {\n        className: \"ms-auto d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n          className: \"me-3\",\n          children: [/*#__PURE__*/_jsxDEV(Dropdown.Toggle, {\n            as: \"button\",\n            style: {\n              ...toggleButtonStyles,\n              position: 'relative'\n            },\n            className: \"position-relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-bell\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                top: '4px',\n                right: '4px',\n                width: '8px',\n                height: '8px',\n                backgroundColor: dattaAbleTheme.colors.error.main,\n                borderRadius: '50%'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown.Menu, {\n            align: \"end\",\n            style: {\n              minWidth: '300px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Dropdown.Header, {\n              children: \"Notifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '32px',\n                      height: '32px',\n                      backgroundColor: dattaAbleTheme.colors.success.main,\n                      borderRadius: '50%',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-check text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 162,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-grow-1 ms-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: \"Welcome to Dashboard\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0 text-muted small\",\n                    children: \"Your dashboard is ready to use\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                children: \"View all notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          show: showProfileDropdown,\n          onToggle: setShowProfileDropdown,\n          children: [/*#__PURE__*/_jsxDEV(Dropdown.Toggle, {\n            as: \"div\",\n            style: {\n              cursor: 'pointer'\n            },\n            className: \"d-flex align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: userAvatarStyles,\n              children: getUserInitials(user === null || user === void 0 ? void 0 : user.name)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ms-2 d-none d-sm-block\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: dattaAbleTheme.typography.fontWeight.medium,\n                  color: dattaAbleTheme.colors.text.primary\n                },\n                children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: dattaAbleTheme.colors.text.secondary\n                },\n                children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-chevron-down ms-2 text-muted\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown.Menu, {\n            align: \"end\",\n            style: {\n              minWidth: '200px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Dropdown.Header, {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: userAvatarStyles,\n                  className: \"mx-auto mb-2\",\n                  children: getUserInitials(user === null || user === void 0 ? void 0 : user.name)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"mb-0\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n              onClick: () => navigate('/profile'),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-user me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this), \"Profile\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n              onClick: () => navigate('/profile/edit'),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-cog me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), \"Settings\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), (user === null || user === void 0 ? void 0 : user.role) === 'admin' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Dropdown.Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n                onClick: handleAdminPanel,\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-shield-alt me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this), \"Admin Panel\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Dropdown.Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n              onClick: handleLogout,\n              className: \"text-danger\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-sign-out-alt me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), \"Logout\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"link\", {\n      rel: \"stylesheet\",\n      href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        .dropdown-toggle::after {\n          display: none;\n        }\n\n        .dropdown-menu {\n          border: 1px solid ${dattaAbleTheme.colors.border};\n          border-radius: ${dattaAbleTheme.borderRadius.lg};\n          box-shadow: ${dattaAbleTheme.shadows.lg};\n          padding: ${dattaAbleTheme.spacing[2]};\n        }\n\n        .dropdown-item {\n          border-radius: ${dattaAbleTheme.borderRadius.md};\n          padding: ${dattaAbleTheme.spacing[2]} ${dattaAbleTheme.spacing[3]};\n          transition: all 0.2s ease;\n        }\n\n        .dropdown-item:hover {\n          background-color: ${dattaAbleTheme.colors.background.light};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .dropdown-header {\n          padding: ${dattaAbleTheme.spacing[3]};\n          font-weight: ${dattaAbleTheme.typography.fontWeight.semibold};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        @media (max-width: 767.98px) {\n          .header-mobile {\n            left: 0 !important;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\n_s(DattaAbleHeader, \"NJMYJbz6pN5Sg7yKzN584bp+t8s=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = DattaAbleHeader;\nexport default DattaAbleHeader;\nvar _c;\n$RefreshReg$(_c, \"DattaAbleHeader\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON>", "Nav", "Dropdown", "<PERSON><PERSON>", "useNavigate", "useAuth", "dattaAbleTheme", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Datta<PERSON>bleHeader", "onToggleSidebar", "onToggleSidebarCollapse", "sidebarCollapsed", "_s", "navigate", "user", "logout", "showProfileDropdown", "setShowProfileDropdown", "handleLogout", "handleAdminPanel", "_process$env$REACT_AP", "adminUrl", "process", "env", "REACT_APP_API_URL", "replace", "window", "open", "headerStyles", "position", "top", "right", "left", "layout", "sidebar", "collapsedWidth", "width", "height", "header", "backgroundColor", "colors", "background", "paper", "borderBottom", "border", "boxShadow", "shadows", "sm", "zIndex", "transition", "padding", "mobileHeaderStyles", "navbarStyles", "spacing", "toggleButtonStyles", "color", "text", "primary", "fontSize", "borderRadius", "md", "userAvatarStyles", "main", "display", "alignItems", "justifyContent", "fontWeight", "typography", "medium", "getUserInitials", "name", "split", "map", "n", "join", "toUpperCase", "slice", "style", "innerWidth", "children", "expand", "className", "onClick", "onMouseEnter", "e", "target", "light", "onMouseLeave", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Toggle", "as", "error", "<PERSON><PERSON>", "align", "min<PERSON><PERSON><PERSON>", "Header", "<PERSON><PERSON>", "success", "Divider", "show", "onToggle", "cursor", "secondary", "email", "role", "rel", "href", "lg", "semibold", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/DattaAbleHeader.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Navbar, Nav, Dropdown, Button } from 'react-bootstrap';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport dattaAbleTheme from '../../theme/dattaAbleTheme';\n\ninterface DattaAbleHeaderProps {\n  onToggleSidebar: () => void;\n  onToggleSidebarCollapse: () => void;\n  sidebarCollapsed: boolean;\n}\n\nconst DattaAbleHeader: React.FC<DattaAbleHeaderProps> = ({ onToggleSidebar, onToggleSidebarCollapse, sidebarCollapsed }) => {\n  const navigate = useNavigate();\n  const { user, logout } = useAuth();\n  const [showProfileDropdown, setShowProfileDropdown] = useState(false);\n\n  const handleLogout = async () => {\n    await logout();\n    navigate('/login');\n  };\n\n  const handleAdminPanel = () => {\n    // Open admin panel in new tab to preserve current session\n    const adminUrl = process.env.REACT_APP_API_URL?.replace('/api', '/admin') || 'http://localhost:8000/admin';\n    window.open(adminUrl, '_blank');\n  };\n\n  const headerStyles: React.CSSProperties = {\n    position: 'fixed',\n    top: 0,\n    right: 0,\n    left: sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width,\n    height: dattaAbleTheme.layout.header.height,\n    backgroundColor: dattaAbleTheme.colors.background.paper,\n    borderBottom: `1px solid ${dattaAbleTheme.colors.border}`,\n    boxShadow: dattaAbleTheme.shadows.sm,\n    zIndex: 1030,\n    transition: 'left 0.3s ease',\n    padding: 0,\n  };\n\n  const mobileHeaderStyles: React.CSSProperties = {\n    ...headerStyles,\n    left: 0,\n  };\n\n  const navbarStyles: React.CSSProperties = {\n    height: '100%',\n    padding: `0 ${dattaAbleTheme.spacing[4]}`,\n  };\n\n  const toggleButtonStyles: React.CSSProperties = {\n    backgroundColor: 'transparent',\n    border: 'none',\n    color: dattaAbleTheme.colors.text.primary,\n    fontSize: '1.25rem',\n    padding: dattaAbleTheme.spacing[2],\n    borderRadius: dattaAbleTheme.borderRadius.md,\n    transition: 'all 0.2s ease',\n  };\n\n  const userAvatarStyles: React.CSSProperties = {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    backgroundColor: dattaAbleTheme.colors.primary.main,\n    color: 'white',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontSize: '0.875rem',\n    fontWeight: dattaAbleTheme.typography.fontWeight.medium,\n  };\n\n  const getUserInitials = (name: string | undefined): string => {\n    if (!name) return 'U';\n    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);\n  };\n\n  return (\n    <div style={window.innerWidth < 768 ? mobileHeaderStyles : headerStyles}>\n      <Navbar expand=\"lg\" style={navbarStyles} className=\"px-0\">\n        <div className=\"d-flex align-items-center\">\n          {/* Mobile Menu Toggle */}\n          <Button\n            style={toggleButtonStyles}\n            onClick={onToggleSidebar}\n            className=\"d-lg-none me-2\"\n            onMouseEnter={(e) => {\n              (e.target as HTMLElement).style.backgroundColor = dattaAbleTheme.colors.background.light;\n            }}\n            onMouseLeave={(e) => {\n              (e.target as HTMLElement).style.backgroundColor = 'transparent';\n            }}\n          >\n            <i className=\"fas fa-bars\"></i>\n          </Button>\n\n          {/* Desktop Sidebar Toggle */}\n          <Button\n            style={toggleButtonStyles}\n            onClick={onToggleSidebarCollapse}\n            className=\"d-none d-lg-block me-3\"\n            onMouseEnter={(e) => {\n              (e.target as HTMLElement).style.backgroundColor = dattaAbleTheme.colors.background.light;\n            }}\n            onMouseLeave={(e) => {\n              (e.target as HTMLElement).style.backgroundColor = 'transparent';\n            }}\n          >\n            <i className={`fas ${sidebarCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'}`}></i>\n          </Button>\n\n          {/* Page Title */}\n          <h5 className=\"mb-0 text-dark fw-semibold\">\n            Dashboard\n          </h5>\n        </div>\n\n        {/* Right Side Navigation */}\n        <Nav className=\"ms-auto d-flex align-items-center\">\n          {/* Notifications */}\n          <Dropdown className=\"me-3\">\n            <Dropdown.Toggle\n              as=\"button\"\n              style={{\n                ...toggleButtonStyles,\n                position: 'relative',\n              }}\n              className=\"position-relative\"\n            >\n              <i className=\"fas fa-bell\"></i>\n              <span\n                style={{\n                  position: 'absolute',\n                  top: '4px',\n                  right: '4px',\n                  width: '8px',\n                  height: '8px',\n                  backgroundColor: dattaAbleTheme.colors.error.main,\n                  borderRadius: '50%',\n                }}\n              ></span>\n            </Dropdown.Toggle>\n            <Dropdown.Menu align=\"end\" style={{ minWidth: '300px' }}>\n              <Dropdown.Header>Notifications</Dropdown.Header>\n              <Dropdown.Item>\n                <div className=\"d-flex\">\n                  <div className=\"flex-shrink-0\">\n                    <div\n                      style={{\n                        width: '32px',\n                        height: '32px',\n                        backgroundColor: dattaAbleTheme.colors.success.main,\n                        borderRadius: '50%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                      }}\n                    >\n                      <i className=\"fas fa-check text-white\"></i>\n                    </div>\n                  </div>\n                  <div className=\"flex-grow-1 ms-3\">\n                    <h6 className=\"mb-1\">Welcome to Dashboard</h6>\n                    <p className=\"mb-0 text-muted small\">\n                      Your dashboard is ready to use\n                    </p>\n                  </div>\n                </div>\n              </Dropdown.Item>\n              <Dropdown.Divider />\n              <Dropdown.Item className=\"text-center\">\n                <small>View all notifications</small>\n              </Dropdown.Item>\n            </Dropdown.Menu>\n          </Dropdown>\n\n          {/* User Profile Dropdown */}\n          <Dropdown show={showProfileDropdown} onToggle={setShowProfileDropdown}>\n            <Dropdown.Toggle\n              as=\"div\"\n              style={{ cursor: 'pointer' }}\n              className=\"d-flex align-items-center\"\n            >\n              <div style={userAvatarStyles}>\n                {getUserInitials(user?.name)}\n              </div>\n              <div className=\"ms-2 d-none d-sm-block\">\n                <div\n                  style={{\n                    fontSize: '0.875rem',\n                    fontWeight: dattaAbleTheme.typography.fontWeight.medium,\n                    color: dattaAbleTheme.colors.text.primary,\n                  }}\n                >\n                  {user?.name || 'User'}\n                </div>\n                <div\n                  style={{\n                    fontSize: '0.75rem',\n                    color: dattaAbleTheme.colors.text.secondary,\n                  }}\n                >\n                  {user?.email || '<EMAIL>'}\n                </div>\n              </div>\n              <i className=\"fas fa-chevron-down ms-2 text-muted\"></i>\n            </Dropdown.Toggle>\n\n            <Dropdown.Menu align=\"end\" style={{ minWidth: '200px' }}>\n              <Dropdown.Header>\n                <div className=\"text-center\">\n                  <div style={userAvatarStyles} className=\"mx-auto mb-2\">\n                    {getUserInitials(user?.name)}\n                  </div>\n                  <h6 className=\"mb-0\">{user?.name || 'User'}</h6>\n                  <small className=\"text-muted\">{user?.email || '<EMAIL>'}</small>\n                </div>\n              </Dropdown.Header>\n              <Dropdown.Divider />\n              <Dropdown.Item onClick={() => navigate('/profile')}>\n                <i className=\"fas fa-user me-2\"></i>\n                Profile\n              </Dropdown.Item>\n              <Dropdown.Item onClick={() => navigate('/profile/edit')}>\n                <i className=\"fas fa-cog me-2\"></i>\n                Settings\n              </Dropdown.Item>\n              {user?.role === 'admin' && (\n                <>\n                  <Dropdown.Divider />\n                  <Dropdown.Item onClick={handleAdminPanel}>\n                    <i className=\"fas fa-shield-alt me-2\"></i>\n                    Admin Panel\n                  </Dropdown.Item>\n                </>\n              )}\n              <Dropdown.Divider />\n              <Dropdown.Item onClick={handleLogout} className=\"text-danger\">\n                <i className=\"fas fa-sign-out-alt me-2\"></i>\n                Logout\n              </Dropdown.Item>\n            </Dropdown.Menu>\n          </Dropdown>\n        </Nav>\n      </Navbar>\n\n      {/* Font Awesome Icons */}\n      <link\n        rel=\"stylesheet\"\n        href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"\n      />\n\n      <style>{`\n        .dropdown-toggle::after {\n          display: none;\n        }\n\n        .dropdown-menu {\n          border: 1px solid ${dattaAbleTheme.colors.border};\n          border-radius: ${dattaAbleTheme.borderRadius.lg};\n          box-shadow: ${dattaAbleTheme.shadows.lg};\n          padding: ${dattaAbleTheme.spacing[2]};\n        }\n\n        .dropdown-item {\n          border-radius: ${dattaAbleTheme.borderRadius.md};\n          padding: ${dattaAbleTheme.spacing[2]} ${dattaAbleTheme.spacing[3]};\n          transition: all 0.2s ease;\n        }\n\n        .dropdown-item:hover {\n          background-color: ${dattaAbleTheme.colors.background.light};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .dropdown-header {\n          padding: ${dattaAbleTheme.spacing[3]};\n          font-weight: ${dattaAbleTheme.typography.fontWeight.semibold};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        @media (max-width: 767.98px) {\n          .header-mobile {\n            left: 0 !important;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default DattaAbleHeader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,iBAAiB;AAC/D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,cAAc,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQxD,MAAMC,eAA+C,GAAGA,CAAC;EAAEC,eAAe;EAAEC,uBAAuB;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EAC1H,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEa,IAAI;IAAEC;EAAO,CAAC,GAAGb,OAAO,CAAC,CAAC;EAClC,MAAM,CAACc,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAMsB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMH,MAAM,CAAC,CAAC;IACdF,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMM,gBAAgB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC7B;IACA,MAAMC,QAAQ,GAAG,EAAAD,qBAAA,GAAAE,OAAO,CAACC,GAAG,CAACC,iBAAiB,cAAAJ,qBAAA,uBAA7BA,qBAAA,CAA+BK,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAI,6BAA6B;IAC1GC,MAAM,CAACC,IAAI,CAACN,QAAQ,EAAE,QAAQ,CAAC;EACjC,CAAC;EAED,MAAMO,YAAiC,GAAG;IACxCC,QAAQ,EAAE,OAAO;IACjBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,IAAI,EAAErB,gBAAgB,GAAGR,cAAc,CAAC8B,MAAM,CAACC,OAAO,CAACC,cAAc,GAAGhC,cAAc,CAAC8B,MAAM,CAACC,OAAO,CAACE,KAAK;IAC3GC,MAAM,EAAElC,cAAc,CAAC8B,MAAM,CAACK,MAAM,CAACD,MAAM;IAC3CE,eAAe,EAAEpC,cAAc,CAACqC,MAAM,CAACC,UAAU,CAACC,KAAK;IACvDC,YAAY,EAAE,aAAaxC,cAAc,CAACqC,MAAM,CAACI,MAAM,EAAE;IACzDC,SAAS,EAAE1C,cAAc,CAAC2C,OAAO,CAACC,EAAE;IACpCC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,gBAAgB;IAC5BC,OAAO,EAAE;EACX,CAAC;EAED,MAAMC,kBAAuC,GAAG;IAC9C,GAAGvB,YAAY;IACfI,IAAI,EAAE;EACR,CAAC;EAED,MAAMoB,YAAiC,GAAG;IACxCf,MAAM,EAAE,MAAM;IACda,OAAO,EAAE,KAAK/C,cAAc,CAACkD,OAAO,CAAC,CAAC,CAAC;EACzC,CAAC;EAED,MAAMC,kBAAuC,GAAG;IAC9Cf,eAAe,EAAE,aAAa;IAC9BK,MAAM,EAAE,MAAM;IACdW,KAAK,EAAEpD,cAAc,CAACqC,MAAM,CAACgB,IAAI,CAACC,OAAO;IACzCC,QAAQ,EAAE,SAAS;IACnBR,OAAO,EAAE/C,cAAc,CAACkD,OAAO,CAAC,CAAC,CAAC;IAClCM,YAAY,EAAExD,cAAc,CAACwD,YAAY,CAACC,EAAE;IAC5CX,UAAU,EAAE;EACd,CAAC;EAED,MAAMY,gBAAqC,GAAG;IAC5CzB,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdsB,YAAY,EAAE,KAAK;IACnBpB,eAAe,EAAEpC,cAAc,CAACqC,MAAM,CAACiB,OAAO,CAACK,IAAI;IACnDP,KAAK,EAAE,OAAO;IACdQ,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBP,QAAQ,EAAE,UAAU;IACpBQ,UAAU,EAAE/D,cAAc,CAACgE,UAAU,CAACD,UAAU,CAACE;EACnD,CAAC;EAED,MAAMC,eAAe,GAAIC,IAAwB,IAAa;IAC5D,IAAI,CAACA,IAAI,EAAE,OAAO,GAAG;IACrB,OAAOA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1E,CAAC;EAED,oBACEvE,OAAA;IAAKwE,KAAK,EAAEnD,MAAM,CAACoD,UAAU,GAAG,GAAG,GAAG3B,kBAAkB,GAAGvB,YAAa;IAAAmD,QAAA,gBACtE1E,OAAA,CAACR,MAAM;MAACmF,MAAM,EAAC,IAAI;MAACH,KAAK,EAAEzB,YAAa;MAAC6B,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACvD1E,OAAA;QAAK4E,SAAS,EAAC,2BAA2B;QAAAF,QAAA,gBAExC1E,OAAA,CAACL,MAAM;UACL6E,KAAK,EAAEvB,kBAAmB;UAC1B4B,OAAO,EAAEzE,eAAgB;UACzBwE,SAAS,EAAC,gBAAgB;UAC1BE,YAAY,EAAGC,CAAC,IAAK;YAClBA,CAAC,CAACC,MAAM,CAAiBR,KAAK,CAACtC,eAAe,GAAGpC,cAAc,CAACqC,MAAM,CAACC,UAAU,CAAC6C,KAAK;UAC1F,CAAE;UACFC,YAAY,EAAGH,CAAC,IAAK;YAClBA,CAAC,CAACC,MAAM,CAAiBR,KAAK,CAACtC,eAAe,GAAG,aAAa;UACjE,CAAE;UAAAwC,QAAA,eAEF1E,OAAA;YAAG4E,SAAS,EAAC;UAAa;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAGTtF,OAAA,CAACL,MAAM;UACL6E,KAAK,EAAEvB,kBAAmB;UAC1B4B,OAAO,EAAExE,uBAAwB;UACjCuE,SAAS,EAAC,wBAAwB;UAClCE,YAAY,EAAGC,CAAC,IAAK;YAClBA,CAAC,CAACC,MAAM,CAAiBR,KAAK,CAACtC,eAAe,GAAGpC,cAAc,CAACqC,MAAM,CAACC,UAAU,CAAC6C,KAAK;UAC1F,CAAE;UACFC,YAAY,EAAGH,CAAC,IAAK;YAClBA,CAAC,CAACC,MAAM,CAAiBR,KAAK,CAACtC,eAAe,GAAG,aAAa;UACjE,CAAE;UAAAwC,QAAA,eAEF1E,OAAA;YAAG4E,SAAS,EAAE,OAAOtE,gBAAgB,GAAG,kBAAkB,GAAG,iBAAiB;UAAG;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eAGTtF,OAAA;UAAI4E,SAAS,EAAC,4BAA4B;UAAAF,QAAA,EAAC;QAE3C;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGNtF,OAAA,CAACP,GAAG;QAACmF,SAAS,EAAC,mCAAmC;QAAAF,QAAA,gBAEhD1E,OAAA,CAACN,QAAQ;UAACkF,SAAS,EAAC,MAAM;UAAAF,QAAA,gBACxB1E,OAAA,CAACN,QAAQ,CAAC6F,MAAM;YACdC,EAAE,EAAC,QAAQ;YACXhB,KAAK,EAAE;cACL,GAAGvB,kBAAkB;cACrBzB,QAAQ,EAAE;YACZ,CAAE;YACFoD,SAAS,EAAC,mBAAmB;YAAAF,QAAA,gBAE7B1E,OAAA;cAAG4E,SAAS,EAAC;YAAa;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BtF,OAAA;cACEwE,KAAK,EAAE;gBACLhD,QAAQ,EAAE,UAAU;gBACpBC,GAAG,EAAE,KAAK;gBACVC,KAAK,EAAE,KAAK;gBACZK,KAAK,EAAE,KAAK;gBACZC,MAAM,EAAE,KAAK;gBACbE,eAAe,EAAEpC,cAAc,CAACqC,MAAM,CAACsD,KAAK,CAAChC,IAAI;gBACjDH,YAAY,EAAE;cAChB;YAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAClBtF,OAAA,CAACN,QAAQ,CAACgG,IAAI;YAACC,KAAK,EAAC,KAAK;YAACnB,KAAK,EAAE;cAAEoB,QAAQ,EAAE;YAAQ,CAAE;YAAAlB,QAAA,gBACtD1E,OAAA,CAACN,QAAQ,CAACmG,MAAM;cAAAnB,QAAA,EAAC;YAAa;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,eAChDtF,OAAA,CAACN,QAAQ,CAACoG,IAAI;cAAApB,QAAA,eACZ1E,OAAA;gBAAK4E,SAAS,EAAC,QAAQ;gBAAAF,QAAA,gBACrB1E,OAAA;kBAAK4E,SAAS,EAAC,eAAe;kBAAAF,QAAA,eAC5B1E,OAAA;oBACEwE,KAAK,EAAE;sBACLzC,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdE,eAAe,EAAEpC,cAAc,CAACqC,MAAM,CAAC4D,OAAO,CAACtC,IAAI;sBACnDH,YAAY,EAAE,KAAK;sBACnBI,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE;oBAClB,CAAE;oBAAAc,QAAA,eAEF1E,OAAA;sBAAG4E,SAAS,EAAC;oBAAyB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtF,OAAA;kBAAK4E,SAAS,EAAC,kBAAkB;kBAAAF,QAAA,gBAC/B1E,OAAA;oBAAI4E,SAAS,EAAC,MAAM;oBAAAF,QAAA,EAAC;kBAAoB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9CtF,OAAA;oBAAG4E,SAAS,EAAC,uBAAuB;oBAAAF,QAAA,EAAC;kBAErC;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAChBtF,OAAA,CAACN,QAAQ,CAACsG,OAAO;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpBtF,OAAA,CAACN,QAAQ,CAACoG,IAAI;cAAClB,SAAS,EAAC,aAAa;cAAAF,QAAA,eACpC1E,OAAA;gBAAA0E,QAAA,EAAO;cAAsB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGXtF,OAAA,CAACN,QAAQ;UAACuG,IAAI,EAAEtF,mBAAoB;UAACuF,QAAQ,EAAEtF,sBAAuB;UAAA8D,QAAA,gBACpE1E,OAAA,CAACN,QAAQ,CAAC6F,MAAM;YACdC,EAAE,EAAC,KAAK;YACRhB,KAAK,EAAE;cAAE2B,MAAM,EAAE;YAAU,CAAE;YAC7BvB,SAAS,EAAC,2BAA2B;YAAAF,QAAA,gBAErC1E,OAAA;cAAKwE,KAAK,EAAEhB,gBAAiB;cAAAkB,QAAA,EAC1BV,eAAe,CAACvD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,IAAI;YAAC;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACNtF,OAAA;cAAK4E,SAAS,EAAC,wBAAwB;cAAAF,QAAA,gBACrC1E,OAAA;gBACEwE,KAAK,EAAE;kBACLnB,QAAQ,EAAE,UAAU;kBACpBQ,UAAU,EAAE/D,cAAc,CAACgE,UAAU,CAACD,UAAU,CAACE,MAAM;kBACvDb,KAAK,EAAEpD,cAAc,CAACqC,MAAM,CAACgB,IAAI,CAACC;gBACpC,CAAE;gBAAAsB,QAAA,EAED,CAAAjE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,IAAI,KAAI;cAAM;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACNtF,OAAA;gBACEwE,KAAK,EAAE;kBACLnB,QAAQ,EAAE,SAAS;kBACnBH,KAAK,EAAEpD,cAAc,CAACqC,MAAM,CAACgB,IAAI,CAACiD;gBACpC,CAAE;gBAAA1B,QAAA,EAED,CAAAjE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4F,KAAK,KAAI;cAAkB;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtF,OAAA;cAAG4E,SAAS,EAAC;YAAqC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eAElBtF,OAAA,CAACN,QAAQ,CAACgG,IAAI;YAACC,KAAK,EAAC,KAAK;YAACnB,KAAK,EAAE;cAAEoB,QAAQ,EAAE;YAAQ,CAAE;YAAAlB,QAAA,gBACtD1E,OAAA,CAACN,QAAQ,CAACmG,MAAM;cAAAnB,QAAA,eACd1E,OAAA;gBAAK4E,SAAS,EAAC,aAAa;gBAAAF,QAAA,gBAC1B1E,OAAA;kBAAKwE,KAAK,EAAEhB,gBAAiB;kBAACoB,SAAS,EAAC,cAAc;kBAAAF,QAAA,EACnDV,eAAe,CAACvD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,IAAI;gBAAC;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNtF,OAAA;kBAAI4E,SAAS,EAAC,MAAM;kBAAAF,QAAA,EAAE,CAAAjE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,IAAI,KAAI;gBAAM;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChDtF,OAAA;kBAAO4E,SAAS,EAAC,YAAY;kBAAAF,QAAA,EAAE,CAAAjE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4F,KAAK,KAAI;gBAAkB;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eAClBtF,OAAA,CAACN,QAAQ,CAACsG,OAAO;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpBtF,OAAA,CAACN,QAAQ,CAACoG,IAAI;cAACjB,OAAO,EAAEA,CAAA,KAAMrE,QAAQ,CAAC,UAAU,CAAE;cAAAkE,QAAA,gBACjD1E,OAAA;gBAAG4E,SAAS,EAAC;cAAkB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,WAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChBtF,OAAA,CAACN,QAAQ,CAACoG,IAAI;cAACjB,OAAO,EAAEA,CAAA,KAAMrE,QAAQ,CAAC,eAAe,CAAE;cAAAkE,QAAA,gBACtD1E,OAAA;gBAAG4E,SAAS,EAAC;cAAiB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,YAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,EACf,CAAA7E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6F,IAAI,MAAK,OAAO,iBACrBtG,OAAA,CAAAE,SAAA;cAAAwE,QAAA,gBACE1E,OAAA,CAACN,QAAQ,CAACsG,OAAO;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpBtF,OAAA,CAACN,QAAQ,CAACoG,IAAI;gBAACjB,OAAO,EAAE/D,gBAAiB;gBAAA4D,QAAA,gBACvC1E,OAAA;kBAAG4E,SAAS,EAAC;gBAAwB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAE5C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA,eAChB,CACH,eACDtF,OAAA,CAACN,QAAQ,CAACsG,OAAO;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpBtF,OAAA,CAACN,QAAQ,CAACoG,IAAI;cAACjB,OAAO,EAAEhE,YAAa;cAAC+D,SAAS,EAAC,aAAa;cAAAF,QAAA,gBAC3D1E,OAAA;gBAAG4E,SAAS,EAAC;cAA0B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,UAE9C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTtF,OAAA;MACEuG,GAAG,EAAC,YAAY;MAChBC,IAAI,EAAC;IAA2E;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjF,CAAC,eAEFtF,OAAA;MAAA0E,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA,8BAA8B5E,cAAc,CAACqC,MAAM,CAACI,MAAM;AAC1D,2BAA2BzC,cAAc,CAACwD,YAAY,CAACmD,EAAE;AACzD,wBAAwB3G,cAAc,CAAC2C,OAAO,CAACgE,EAAE;AACjD,qBAAqB3G,cAAc,CAACkD,OAAO,CAAC,CAAC,CAAC;AAC9C;AACA;AACA;AACA,2BAA2BlD,cAAc,CAACwD,YAAY,CAACC,EAAE;AACzD,qBAAqBzD,cAAc,CAACkD,OAAO,CAAC,CAAC,CAAC,IAAIlD,cAAc,CAACkD,OAAO,CAAC,CAAC,CAAC;AAC3E;AACA;AACA;AACA;AACA,8BAA8BlD,cAAc,CAACqC,MAAM,CAACC,UAAU,CAAC6C,KAAK;AACpE,mBAAmBnF,cAAc,CAACqC,MAAM,CAACgB,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,qBAAqBtD,cAAc,CAACkD,OAAO,CAAC,CAAC,CAAC;AAC9C,yBAAyBlD,cAAc,CAACgE,UAAU,CAACD,UAAU,CAAC6C,QAAQ;AACtE,mBAAmB5G,cAAc,CAACqC,MAAM,CAACgB,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC/E,EAAA,CAxRIJ,eAA+C;EAAA,QAClCP,WAAW,EACHC,OAAO;AAAA;AAAA8G,EAAA,GAF5BxG,eAA+C;AA0RrD,eAAeA,eAAe;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}